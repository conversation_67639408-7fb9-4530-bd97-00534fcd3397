@echo off
chcp 65001 >nul
echo.
echo ========================================
echo     🔒 اختبار حماية API على العنوان الخارجي
echo ========================================
echo.

echo 🎯 اختبار العنوان: http://***********:8080/api/users
echo.

echo 🧪 اختبار 1: محاولة الوصول بدون Token...
echo ================================================
echo.

powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://***********:8080/api/users' -Method Get -TimeoutSec 10; Write-Host '❌ خطر أمني! API غير محمي - تم الوصول بدون Token'; Write-Host 'البيانات المكشوفة:'; $response | ConvertTo-Json -Depth 2 } catch { if ($_.Exception.Response.StatusCode -eq 401) { Write-Host '✅ ممتاز! API محمي - تم رفض الوصول بدون Token'; Write-Host 'Status: 401 Unauthorized' } else { Write-Host '⚠️ خطأ في الاتصال:' $_.Exception.Message } }"

echo.
echo ================================================
echo.

echo 🧪 اختبار 2: تسجيل الدخول للحصول على Token...
echo ================================================
echo.

powershell -Command "$loginData = @{ loginName='admin'; password='admin123456'; deviceId='test_external_device' } | ConvertTo-Json; try { $response = Invoke-RestMethod -Uri 'http://***********:8080/api/auth/login' -Method Post -Body $loginData -ContentType 'application/json' -TimeoutSec 10; if ($response.success -and $response.token) { Write-Host '✅ تم تسجيل الدخول بنجاح'; Write-Host 'المستخدم:' $response.user.username; Write-Host 'Token:' $response.token.Substring(0, 50) '...'; $global:token = $response.token } else { Write-Host '❌ فشل تسجيل الدخول'; Write-Host 'الخطأ:' $response.error } } catch { Write-Host '❌ خطأ في تسجيل الدخول:' $_.Exception.Message }"

echo.
echo ================================================
echo.

echo 🧪 اختبار 3: الوصول مع Token صحيح...
echo ================================================
echo.

powershell -Command "if ($global:token) { try { $headers = @{ Authorization = 'Bearer ' + $global:token }; $response = Invoke-RestMethod -Uri 'http://***********:8080/api/users' -Method Get -Headers $headers -TimeoutSec 10; Write-Host '✅ تم الوصول بنجاح مع Token صحيح'; Write-Host 'عدد المستخدمين:' $response.data.Count; Write-Host 'إجمالي السجلات:' $response.total } catch { Write-Host '❌ فشل الوصول مع Token صحيح:' $_.Exception.Message } } else { Write-Host '❌ لا يوجد Token للاختبار' }"

echo.
echo ================================================
echo.

echo 🧪 اختبار 4: الوصول مع Token خاطئ...
echo ================================================
echo.

powershell -Command "try { $headers = @{ Authorization = 'Bearer invalid_fake_token_123456789' }; $response = Invoke-RestMethod -Uri 'http://***********:8080/api/users' -Method Get -Headers $headers -TimeoutSec 10; Write-Host '❌ خطر أمني! تم قبول Token خاطئ'; Write-Host 'البيانات:' ($response | ConvertTo-Json -Depth 1) } catch { if ($_.Exception.Response.StatusCode -eq 401) { Write-Host '✅ ممتاز! تم رفض Token خاطئ'; Write-Host 'Status: 401 Unauthorized' } else { Write-Host '⚠️ خطأ غير متوقع:' $_.Exception.Message } }"

echo.
echo ========================================
echo           ملخص النتائج
echo ========================================
echo.

echo 📊 تفسير النتائج:
echo.
echo ✅ إذا ظهر "API محمي" في الاختبار 1 و 4 = النظام آمن
echo ❌ إذا ظهر "خطر أمني" في أي اختبار = يحتاج إصلاح
echo ⚠️ إذا ظهر "خطأ في الاتصال" = مشكلة في الشبكة
echo.

echo 💡 إذا كان النظام غير محمي:
echo    1. تأكد من تشغيل الخادم المحمي: run-protected-server.bat
echo    2. تحقق من أن working-server.js يحتوي على JWT protection
echo    3. أعد تشغيل الخادم بعد التعديلات
echo.

pause
