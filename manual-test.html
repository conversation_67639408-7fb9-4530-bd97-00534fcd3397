<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار API المطورين - سريع</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-btn { padding: 15px 25px; margin: 10px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; }
        .test-success { background: #28a745; color: white; }
        .test-error { background: #dc3545; color: white; }
        .test-warning { background: #ffc107; color: black; }
        .test-info { background: #17a2b8; color: white; }
        .result { margin: 20px 0; padding: 15px; border-radius: 8px; font-family: monospace; }
        .result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .status { font-weight: bold; font-size: 18px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API المطورين - سريع</h1>
        
        <h2>🎯 اختبارات سريعة</h2>
        <div style="text-align: center;">
            <button class="test-btn test-success" onclick="testClient1000()">✅ عميل نشط (1000)</button>
            <button class="test-btn test-error" onclick="testClient1004()">❌ توكن خاطئ (1004)</button>
            <button class="test-btn test-warning" onclick="testClient1005()">🚫 عميل محظور (1005)</button>
            <button class="test-btn test-info" onclick="testClient9999()">❓ عميل غير موجود (9999)</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_URL = 'http://***********:8080/api/external/verify-direct';
        
        function showResult(title, data, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="status">الحالة: ${data.status || 'غير محدد'}</div>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        async function testAPI(clientCode, token, description) {
            const tokenMD5 = CryptoJS.MD5(token).toString();
            
            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        agent_login_name: 'testuser',
                        agent_login_password: 'test123',
                        client_code: clientCode,
                        client_token: tokenMD5
                    })
                });

                const data = await response.json();
                
                let type = 'info';
                if (data.status === 'success' && data.client_status === 1) type = 'success';
                else if (data.status === 'success' && data.client_status === 2) type = 'warning';
                else if (data.status === 'client_error' || data.status === 'agent_error') type = 'error';
                
                showResult(`${description} - HTTP ${response.status}`, data, type);
                
            } catch (error) {
                showResult(`${description} - خطأ في الاتصال`, { error: error.message }, 'error');
            }
        }

        function testClient1000() {
            testAPI('1000', 'ABC12345', '✅ عميل نشط (1000)');
        }

        function testClient1004() {
            testAPI('1004', 'TEST1004', '❌ توكن خاطئ (1004)');
        }

        function testClient1005() {
            testAPI('1005', 'TEST1005', '🚫 عميل محظور (1005)');
        }

        function testClient9999() {
            testAPI('9999', 'DEMO999', '❓ عميل غير موجود (9999)');
        }

        // Auto-test on page load
        window.onload = function() {
            setTimeout(() => {
                console.log('🚀 بدء الاختبارات التلقائية...');
                testClient1000();
                setTimeout(() => testClient1004(), 1000);
                setTimeout(() => testClient1005(), 2000);
                setTimeout(() => testClient9999(), 3000);
            }, 1000);
        };
    </script>
</body>
</html>
