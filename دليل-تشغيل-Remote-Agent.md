# 🚀 دليل تشغيل خدمة Remote Agent - نظام إدارة العملاء اليمني

## 📋 متطلبات النظام

### البرامج المطلوبة:
- **Node.js** (الإصدار 18 أو أحدث) - [تحميل من هنا](https://nodejs.org)
- **PostgreSQL** (الإصدار 12 أو أحدث) - [تحميل من هنا](https://www.postgresql.org/download/)
- **npm** (يأتي مع Node.js)

### متطلبات النظام:
- **نظام التشغيل:** Windows 10/11, Linux, macOS
- **الذاكرة:** 4GB RAM كحد أدنى
- **المساحة:** 2GB مساحة فارغة
- **الشبكة:** اتصال إنترنت مستقر

---

## 🔧 طرق التشغيل

### **الطريقة الأولى: التشغيل التلقائي (الأسهل)**

1. **انقر مرتين** على ملف `setup-remote-agent.bat`
2. اتبع التعليمات على الشاشة
3. انتظر حتى اكتمال الإعداد

### **الطريقة الثانية: التشغيل اليدوي**

#### 1. إعداد قاعدة البيانات:
```bash
# إنشاء قاعدة البيانات
psql -U postgres -c "CREATE DATABASE yemclient_db;"

# تشغيل سكريبت إنشاء الجداول
psql -U postgres -d yemclient_db -f create_database.sql
```

#### 2. تثبيت المتطلبات:
```bash
# تثبيت متطلبات المشروع الرئيسي
npm install

# تثبيت متطلبات الخادم
cd server
npm install

# تثبيت متطلبات العميل
cd ../client
npm install
cd ..
```

#### 3. تشغيل الخدمات:
```bash
# تشغيل الخادم
cd server
npm start

# في نافذة أخرى - تشغيل العميل
cd client
npm run dev
```

### **الطريقة الثالثة: التشغيل المتقدم**

#### استخدام الأوامر المتاحة:
```bash
# تشغيل النظام كاملاً
npm run dev

# تشغيل الخادم فقط
npm run server

# تشغيل العميل فقط
npm run client

# بناء المشروع للإنتاج
npm run build
```

---

## 🌐 الوصول للنظام

### **الروابط الأساسية:**
- **واجهة العميل:** http://localhost:5173
- **API الخادم المحلي:** http://localhost:8081
- **API الخادم الخارجي:** http://***********:8081
- **API الوكلاء:** http://***********:8081/api/external/

### **صفحات الاختبار:**
- **فحص حالة الخادم:** http://localhost:8081/api/external/health
- **اختبار API:** http://localhost:8081/api/external/verify-direct

---

## 🔑 بيانات الدخول الافتراضية

### **لوحة التحكم:**
- **Username:** admin
- **Password:** admin123456

### **قاعدة البيانات:**
- **Host:** localhost
- **Port:** 5432
- **Database:** yemclient_db
- **Username:** postgres
- **Password:** yemen123

### **بيانات اختبار الوكلاء:**
| النوع | المعرف | كلمة المرور/التوكن | الحالة |
|-------|--------|-------------------|--------|
| وكيل اختبار | testuser | test123 | نشط |
| عميل 1 | 1004 | TEST1004 | نشط |
| عميل 2 | 1005 | TEST1005 | غير نشط |
| عميل 3 | 9999 | DUMMY999 | نشط |

---

## 🧪 اختبار API الوكلاء

### **النموذج الجديد (مُحسن):**
```json
POST http://***********:8081/api/external/verify-direct
Content-Type: application/json

{
  "username": "testuser",
  "password": "test123",
  "clientCode": 1004,
  "clientToken": "TEST1004",
  "deviceId": "DEV001"
}
```

### **النموذج القديم (للتوافق):**
```json
POST http://***********:8081/api/external/verify-direct
Content-Type: application/json

{
  "agent_login_name": "testuser",
  "agent_login_password": "test123",
  "client_code": 1004,
  "client_token": "TEST1004"
}
```

### **اختبار باستخدام curl:**
```bash
curl -X POST http://***********:8081/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "test123", "clientCode": 1004, "clientToken": "TEST1004"}'
```

---

## 🛠️ استكشاف الأخطاء

### **إذا لم يعمل الخادم:**
1. تأكد من تثبيت **Node.js** بشكل صحيح
2. تحقق من وجود ملف `server/index.js`
3. تأكد من أن المنفذ 8081 غير مستخدم
4. تحقق من اتصال قاعدة البيانات

### **إذا ظهر خطأ "Database connection failed":**
1. تأكد من تشغيل PostgreSQL
2. تحقق من كلمة مرور postgres
3. تأكد من وجود قاعدة البيانات yemclient_db
4. تحقق من ملف `.env` في مجلد server

### **إذا ظهر خطأ "API not found":**
1. تأكد من تشغيل الخادم أولاً
2. تحقق من الرابط: http://localhost:8081/api/external/health
3. تحقق من سجلات الخادم في نافذة الأوامر

### **للحصول على المساعدة:**
- افتح `test-server-simple.html` في المتصفح لاختبار شامل
- تحقق من سجلات الخادم في نافذة الأوامر
- راجع ملف `logs/server-startup.log`

---

## 🔒 الحماية والأمان

✅ **تم تطبيق الحماية الكاملة:**
- جميع APIs الداخلية محمية بـ JWT Token
- APIs المطورين الخارجية متاحة بدون token
- فحص صحة بيانات الوكيل والعميل
- تسجيل جميع العمليات في قاعدة البيانات
- حماية من SQL Injection و XSS
- تحديد معدل الطلبات (Rate Limiting)

---

## 📞 الدعم الفني

إذا واجهت أي مشاكل، تأكد من:
1. ✅ تشغيل الخادم بنجاح
2. ✅ الاتصال بقاعدة البيانات
3. ✅ صحة بيانات الاختبار
4. ✅ عدم حجب الجدار الناري للمنفذ 8081

### **ملفات مفيدة للمراجعة:**
- `README-تشغيل-الخادم.md` - تعليمات تشغيل مفصلة
- `TROUBLESHOOTING.md` - دليل استكشاف الأخطاء
- `API-Documentation-EN.md` - توثيق API
- `server/.env` - إعدادات الخادم
