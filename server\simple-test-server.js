const express = require('express');
const cors = require('cors');
const crypto = require('crypto');

const app = express();
const PORT = 8081;

// Middleware
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json());

// Test data
const TEST_AGENT = {
  loginName: 'testuser',
  password: 'test123'
};

const TEST_CLIENTS = [
  { code: 1000, token: 'ABC12345', status: 1 },
  { code: 1004, token: 'TEST1004', status: 1 },
  { code: 1005, token: 'TEST1005', status: 0 },
  { code: 9999, token: 'DEMO999', status: 1 }
];

// Health check endpoint
app.get('/api/external/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'Simple test server is running',
    timestamp: new Date().toISOString()
  });
});

// Developer API endpoint
app.post('/api/external/verify-direct', (req, res) => {
  console.log('🎯 API Called:', req.body);
  
  try {
    const {
      agent_login_name,
      agent_login_password,
      client_code,
      client_token
    } = req.body;

    // Validate required fields
    if (!agent_login_name || !agent_login_password || !client_code || !client_token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields',
        error_code: 'VALIDATION_ERROR'
      });
    }

    // Check agent credentials
    if (agent_login_name !== TEST_AGENT.loginName || agent_login_password !== TEST_AGENT.password) {
      console.log('❌ Agent error');
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    // Find client
    const client = TEST_CLIENTS.find(c => c.code === parseInt(client_code));
    if (!client) {
      console.log('❌ Client not found');
      return res.status(404).json({
        status: 'client_error'
      });
    }

    // Verify token (MD5)
    const clientTokenMD5 = crypto.createHash('md5').update(client.token).digest('hex');
    if (clientTokenMD5 !== client_token) {
      console.log('❌ Token mismatch');
      console.log(`Expected: ${clientTokenMD5}, Got: ${client_token}`);
      return res.status(401).json({
        status: 'client_error'
      });
    }

    console.log('✅ Success');
    res.json({
      status: 'success',
      client_status: client.status
    });

  } catch (error) {
    console.error('❌ Error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Internal server error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Simple Test Server running on http://0.0.0.0:${PORT}`);
  console.log(`🌐 External: http://***********:${PORT}`);
  console.log(`🏠 Local: http://localhost:${PORT}`);
  console.log('');
  console.log('📋 Test Data:');
  console.log('Agent:', TEST_AGENT);
  console.log('Clients:', TEST_CLIENTS);
  console.log('');
  console.log('🧪 Test with:');
  console.log(`curl -X POST http://***********:${PORT}/api/external/verify-direct \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -d '{"agent_login_name":"testuser","agent_login_password":"test123","client_code":"1000","client_token":"902fbdd2b1df0c4f70b4a5d23525e932"}'`);
});
