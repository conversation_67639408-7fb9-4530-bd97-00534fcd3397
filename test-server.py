#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

PORT = 8080
DIRECTORY = "client/dist"

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_GET(self):
        # إذا كان المسار لا يحتوي على امتداد ملف، أرسل index.html
        if '.' not in self.path or self.path.endswith('/'):
            self.path = '/index.html'
        return super().do_GET()

def main():
    print("🚀 تشغيل خادم اختبار الإصلاحات...")
    print(f"📁 المجلد: {os.path.abspath(DIRECTORY)}")
    print(f"🌐 الرابط: http://localhost:{PORT}")
    print()
    print("✅ الإصلاحات المطبقة:")
    print("   🔐 كلمة المرور: تظهر نجمات في وضع التعديل")
    print("   👁️ أيقونة العين: تحويل للقراءة فقط")
    print("   📱 حقول الأجهزة: معرف الجهاز 1 و 2")
    print()
    
    # التحقق من وجود المجلد
    if not os.path.exists(DIRECTORY):
        print(f"❌ المجلد {DIRECTORY} غير موجود")
        return
    
    # التحقق من وجود index.html
    index_file = os.path.join(DIRECTORY, "index.html")
    if not os.path.exists(index_file):
        print(f"❌ الملف {index_file} غير موجود")
        return
    
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"🌟 الخادم يعمل على http://localhost:{PORT}")
            print("🔧 اضغط Ctrl+C للإيقاف")
            print()
            
            # فتح المتصفح تلقائياً
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print("⚠️ لم يتم فتح المتصفح تلقائياً - افتحه يدوياً")
            
            print()
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()
