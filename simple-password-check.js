// فحص بسيط لكلمة مرور المستخدم hash8080 بدون Prisma
const fs = require('fs');
const path = require('path');

console.log('🔍 فحص كلمة مرور المستخدم hash8080');
console.log('=' .repeat(50));

// قراءة ملف seed.js للتحقق من كلمة المرور الافتراضية
const seedFilePath = path.join(__dirname, 'server', 'prisma', 'seed.js');

try {
  if (fs.existsSync(seedFilePath)) {
    const seedContent = fs.readFileSync(seedFilePath, 'utf8');
    
    console.log('✅ تم العثور على ملف seed.js');
    
    // البحث عن كلمة مرور hash8080
    const hash8080Match = seedContent.match(/loginName:\s*['"]hash8080['"][\s\S]*?password:\s*await\s+bcrypt\.hash\(['"]([^'"]+)['"]/);
    
    if (hash8080Match) {
      const passwordFromSeed = hash8080Match[1];
      console.log(`🔑 كلمة المرور من ملف seed.js: ${passwordFromSeed}`);
    } else {
      console.log('❌ لم يتم العثور على كلمة مرور hash8080 في seed.js');
    }
    
    // البحث عن جميع كلمات المرور في الملف
    const allPasswordMatches = seedContent.match(/password:\s*await\s+bcrypt\.hash\(['"]([^'"]+)['"]/g);
    
    if (allPasswordMatches) {
      console.log('\n📋 جميع كلمات المرور الموجودة في seed.js:');
      allPasswordMatches.forEach((match, index) => {
        const password = match.match(/['"]([^'"]+)['"]/)[1];
        console.log(`   ${index + 1}. ${password}`);
      });
    }
    
  } else {
    console.log('❌ ملف seed.js غير موجود');
  }
  
  // قراءة ملف .env للتحقق من إعدادات قاعدة البيانات
  const envFilePath = path.join(__dirname, 'server', '.env');
  
  if (fs.existsSync(envFilePath)) {
    const envContent = fs.readFileSync(envFilePath, 'utf8');
    console.log('\n🔧 إعدادات قاعدة البيانات من .env:');
    
    const dbUrlMatch = envContent.match(/DATABASE_URL\s*=\s*["']?([^"'\n]+)["']?/);
    if (dbUrlMatch) {
      console.log(`   DATABASE_URL: ${dbUrlMatch[1]}`);
    }
  } else {
    console.log('\n❌ ملف .env غير موجود');
  }
  
  // قراءة ملف working-server.js للتحقق من إعدادات الخادم
  const serverFilePath = path.join(__dirname, 'server', 'working-server.js');
  
  if (fs.existsSync(serverFilePath)) {
    const serverContent = fs.readFileSync(serverFilePath, 'utf8');
    
    // البحث عن منفذ الخادم
    const portMatch = serverContent.match(/PORT\s*=\s*process\.env\.PORT\s*\|\|\s*(\d+)/);
    if (portMatch) {
      console.log(`\n🌐 منفذ الخادم: ${portMatch[1]}`);
    }
    
    // البحث عن إعدادات قاعدة البيانات
    const dbConfigMatch = serverContent.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(\w+)/);
    if (dbConfigMatch) {
      console.log('\n🗄️ إعدادات قاعدة البيانات:');
      console.log(`   المستخدم: ${dbConfigMatch[1]}`);
      console.log(`   كلمة المرور: ${dbConfigMatch[2]}`);
      console.log(`   الخادم: ${dbConfigMatch[3]}`);
      console.log(`   المنفذ: ${dbConfigMatch[4]}`);
      console.log(`   قاعدة البيانات: ${dbConfigMatch[5]}`);
    }
  }
  
  console.log('\n📋 الخلاصة:');
  console.log('=' .repeat(30));
  console.log('🎯 بيانات تسجيل الدخول المتوقعة:');
  console.log('   اسم المستخدم: hash8080');
  console.log('   كلمة المرور: Hash8080 (بحرف H كبير)');
  console.log('   الرابط: http://127.0.0.1:8080/');
  
  console.log('\n💡 إذا لم تعمل كلمة المرور Hash8080:');
  console.log('   1. جرب: hash8080 (بأحرف صغيرة)');
  console.log('   2. جرب: HASH8080 (بأحرف كبيرة)');
  console.log('   3. تأكد من تشغيل الخادم على المنفذ 8080');
  console.log('   4. تأكد من وجود قاعدة البيانات');
  
} catch (error) {
  console.error('❌ خطأ في قراءة الملفات:', error.message);
}

console.log('\n🏁 انتهى الفحص');
