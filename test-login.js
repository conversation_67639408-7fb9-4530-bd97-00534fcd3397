// اختبار تسجيل الدخول
const axios = require('axios');

async function testLogin() {
  try {
    console.log('🔐 Testing login...');
    
    const response = await axios.post('http://localhost:8080/api/auth/login', {
      loginName: 'admin',
      password: 'admin123456',
      deviceId: 'test-device-123'
    });
    
    console.log('✅ Login successful!');
    console.log('Response:', response.data);
    
    // Test getting users with token
    if (response.data.token) {
      console.log('\n📋 Testing users API...');
      const usersResponse = await axios.get('http://localhost:8080/api/users', {
        headers: {
          'Authorization': `Bearer ${response.data.token}`
        }
      });
      console.log('✅ Users API works!');
      console.log('Users count:', usersResponse.data.users?.length || 0);
    }
    
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
  }
}

testLogin();
