@echo off
echo ========================================
echo    تشغيل working-server.js الصحيح
echo ========================================

echo.
echo 1. إيقاف جميع خوادم Node.js...
taskkill /f /im node.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ تم إيقاف الخوادم السابقة
) else (
    echo    ℹ️  لا توجد خوادم تعمل
)

echo.
echo 2. انتظار 3 ثوانِ...
timeout /t 3 /nobreak >nul

echo.
echo 3. الانتقال إلى مجلد server...
cd /d "%~dp0server"
if %errorlevel% neq 0 (
    echo    ❌ خطأ: لا يمكن الوصول لمجلد server
    pause
    exit /b 1
)

echo    ✅ تم الانتقال إلى: %cd%

echo.
echo 4. تشغيل working-server.js...
echo    🚀 بدء التشغيل على المنفذ 8080...
echo.

node working-server.js

echo.
echo ========================================
echo         انتهى تشغيل الخادم
echo ========================================
pause
