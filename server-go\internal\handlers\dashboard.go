package handlers

import (
	"encoding/json"
	"net/http"
	"yemclient-server/internal/database"
	"yemclient-server/internal/models"
)

func clientDashboardHandler(w http.ResponseWriter, r *http.Request) {
	userID := r.Context().Value("userID").(uint)

	var client models.Client
	if err := database.DB.Where("user_id = ?", userID).First(&client).Error; err != nil {
		http.Error(w, "Client not found", http.StatusNotFound)
		return
	}

	dashboardData := struct {
		Client      models.Client `json:"client"`
		ActiveUsers int           `json:"activeUsers"`
		Records     int           `json:"records"`
	}{
		Client:      client,
		ActiveUsers: getActiveUsersCount(client.ID),
		Records:     getRecordsCount(client.ID),
	}

	json.NewEncoder(w).Encode(dashboardData)
}

func adminStatsHandler(w http.ResponseWriter, r *http.Request) {
	stats := struct {
		TotalUsers   int `json:"totalUsers"`
		ActiveUsers  int `json:"activeUsers"`
		TotalClients int `json:"totalClients"`
		TotalAgents  int `json:"totalAgents"`
		TotalRecords int `json:"totalRecords"`
	}{
		TotalUsers:   getTotalUsersCount(),
		ActiveUsers:  getActiveUsersCount(0),
		TotalClients: getTotalClientsCount(),
		TotalAgents:  getTotalAgentsCount(),
		TotalRecords: getTotalRecordsCount(),
	}

	json.NewEncoder(w).Encode(stats)
}

func recentActivityHandler(w http.ResponseWriter, r *http.Request) {
	var activities []models.DataRecord
	if err := database.DB.Order("created_at desc").Limit(10).Find(&activities).Error; err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(activities)
}

// Helper functions
func getActiveUsersCount(clientID uint) int {
	var count int64
	query := database.DB.Model(&models.User{}).Where("is_active = ?", true)
	if clientID > 0 {
		query = query.Where("client_id = ?", clientID)
	}
	query.Count(&count)
	return int(count)
}

func getRecordsCount(clientID uint) int {
	var count int64
	database.DB.Model(&models.DataRecord{}).Where("client_id = ?", clientID).Count(&count)
	return int(count)
}

func getTotalUsersCount() int {
	var count int64
	database.DB.Model(&models.User{}).Count(&count)
	return int(count)
}

func getTotalClientsCount() int {
	var count int64
	database.DB.Model(&models.Client{}).Count(&count)
	return int(count)
}

func getTotalAgentsCount() int {
	var count int64
	database.DB.Model(&models.Agent{}).Count(&count)
	return int(count)
}

func getTotalRecordsCount() int {
	var count int64
	database.DB.Model(&models.DataRecord{}).Count(&count)
	return int(count)
}
