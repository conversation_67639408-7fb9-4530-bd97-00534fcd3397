// فحص حالة الخادم
const http = require('http');

console.log('🔍 فحص حالة الخادم...');

function checkServer(port) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/api/external/health',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ الخادم يعمل على المنفذ ${port} - Status: ${res.statusCode}`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ الخادم لا يعمل على المنفذ ${port} - ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`⏰ انتهت مهلة الاتصال للمنفذ ${port}`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function checkAllPorts() {
  console.log('🚀 فحص المنافذ...\n');
  
  const port8080 = await checkServer(8080);
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const port8081 = await checkServer(8081);
  
  console.log('\n📋 النتائج:');
  console.log('===========');
  console.log(`المنفذ 8080: ${port8080 ? '✅ يعمل' : '❌ لا يعمل'}`);
  console.log(`المنفذ 8081: ${port8081 ? '✅ يعمل' : '❌ لا يعمل'}`);
  
  if (!port8080 && !port8081) {
    console.log('\n🚨 لا يوجد خادم يعمل!');
    console.log('📝 لتشغيل الخادم:');
    console.log('   1. انقر مرتين على start-server.cmd');
    console.log('   2. أو شغل الأمر: node server/working-server.js');
  }
}

checkAllPorts().catch(console.error);
