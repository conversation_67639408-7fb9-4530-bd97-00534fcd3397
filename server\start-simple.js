// تشغيل الخادم بطريقة بسيطة
console.log('🚀 بدء تشغيل الخادم...');

try {
    // تحميل المتطلبات
    const express = require('express');
    const cors = require('cors');
    const path = require('path');
    
    console.log('✅ تم تحميل Express بنجاح');
    
    // إنشاء التطبيق
    const app = express();
    const PORT = 8080;
    
    // إعداد CORS
    app.use(cors({
        origin: ['http://localhost:3000', 'http://127.0.0.1:3000', 'file://'],
        credentials: true
    }));
    
    // إعداد JSON parsing
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    console.log('✅ تم إعداد Middleware بنجاح');
    
    // تقديم الملفات الثابتة
    app.use(express.static(path.join(__dirname, '../client/dist')));
    
    // API بسيط للاختبار
    app.get('/api/test', (req, res) => {
        res.json({ 
            message: 'الخادم يعمل بنجاح!', 
            timestamp: new Date().toISOString(),
            status: 'success'
        });
    });
    
    // API للمستخدمين (بدون قاعدة بيانات للاختبار)
    app.get('/api/users', (req, res) => {
        res.json({
            data: [
                {
                    id: 1,
                    username: 'مستخدم تجريبي',
                    loginName: 'testuser',
                    deviceId: 'device_test_001',
                    device1: 'device_test_002',
                    isActive: true
                }
            ],
            total: 1,
            page: 1,
            totalPages: 1
        });
    });
    
    // تقديم index.html لجميع المسارات الأخرى
    app.get('*', (req, res) => {
        res.sendFile(path.join(__dirname, '../client/dist/index.html'));
    });
    
    // بدء الخادم
    app.listen(PORT, () => {
        console.log(`🎉 الخادم يعمل على المنفذ ${PORT}`);
        console.log(`🌐 افتح المتصفح على: http://localhost:${PORT}`);
        console.log(`📋 API للاختبار: http://localhost:${PORT}/api/test`);
        console.log(`👥 API المستخدمين: http://localhost:${PORT}/api/users`);
    });
    
} catch (error) {
    console.error('❌ خطأ في تشغيل الخادم:', error);
    process.exit(1);
}
