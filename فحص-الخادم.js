// فحص حالة الخادم والاتصال
const http = require('http');

console.log('🔍 فحص حالة الخادم...\n');

// فحص الخادم المحلي
function checkLocalServer() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 8080,
      path: '/api/external/health',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ الخادم المحلي يعمل:');
        console.log(`   المنفذ: 8080`);
        console.log(`   رمز الاستجابة: ${res.statusCode}`);
        try {
          const json = JSON.parse(data);
          console.log(`   الحالة: ${json.status}`);
          console.log(`   الرسالة: ${json.message}`);
        } catch (e) {
          console.log(`   البيانات: ${data}`);
        }
        resolve(true);
      });
    });

    req.on('error', (error) => {
      console.log('❌ الخادم المحلي لا يعمل:');
      console.log(`   الخطأ: ${error.message}`);
      console.log('   تأكد من تشغيل: cd server && node working-server.js');
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال بالخادم المحلي');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// فحص الخادم الخارجي
function checkExternalServer() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: '***********',
      port: 8080,
      path: '/api/external/health',
      method: 'GET',
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ الخادم الخارجي يعمل:');
        console.log(`   العنوان: ***********:8080`);
        console.log(`   رمز الاستجابة: ${res.statusCode}`);
        try {
          const json = JSON.parse(data);
          console.log(`   الحالة: ${json.status}`);
          console.log(`   الرسالة: ${json.message}`);
        } catch (e) {
          console.log(`   البيانات: ${data}`);
        }
        resolve(true);
      });
    });

    req.on('error', (error) => {
      console.log('❌ الخادم الخارجي لا يعمل:');
      console.log(`   الخطأ: ${error.message}`);
      console.log('   قد يكون الخادم متوقف أو هناك مشكلة في الشبكة');
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال بالخادم الخارجي');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// اختبار API مباشر
function testAPIDirectly() {
  return new Promise((resolve) => {
    const postData = JSON.stringify({
      agent_login_name: 'testuser',
      agent_login_password: 'test123',
      client_code: 1000,
      client_token: 'ABC12345'
    });

    const req = http.request({
      hostname: 'localhost',
      port: 8080,
      path: '/api/external/verify-direct',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('🧪 اختبار API مباشر:');
        console.log(`   رمز الاستجابة: ${res.statusCode}`);
        try {
          const json = JSON.parse(data);
          console.log(`   النتيجة: ${JSON.stringify(json, null, 2)}`);
        } catch (e) {
          console.log(`   البيانات الخام: ${data}`);
        }
        resolve(true);
      });
    });

    req.on('error', (error) => {
      console.log('❌ فشل اختبار API:');
      console.log(`   الخطأ: ${error.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة اختبار API');
      req.destroy();
      resolve(false);
    });

    req.write(postData);
    req.end();
  });
}

// تشغيل الفحوصات
async function runChecks() {
  console.log('🚀 بدء فحص شامل للخادم...\n');
  
  console.log('1️⃣ فحص الخادم المحلي:');
  console.log('========================');
  const localOK = await checkLocalServer();
  
  console.log('\n2️⃣ فحص الخادم الخارجي:');
  console.log('=========================');
  const externalOK = await checkExternalServer();
  
  if (localOK) {
    console.log('\n3️⃣ اختبار API مباشر:');
    console.log('=====================');
    await testAPIDirectly();
  }
  
  console.log('\n📋 ملخص النتائج:');
  console.log('================');
  console.log(`   الخادم المحلي: ${localOK ? '✅ يعمل' : '❌ لا يعمل'}`);
  console.log(`   الخادم الخارجي: ${externalOK ? '✅ يعمل' : '❌ لا يعمل'}`);
  
  if (!localOK && !externalOK) {
    console.log('\n🔧 الحلول المقترحة:');
    console.log('==================');
    console.log('1. تشغيل الخادم: cd server && node working-server.js');
    console.log('2. التحقق من المنفذ 8080 غير مستخدم');
    console.log('3. التحقق من إعدادات الجدار الناري');
    console.log('4. استخدام localhost بدلاً من *********** في صفحة الاختبار');
  }
}

runChecks();
