<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 اختبار حماية API - نظام إدارة العملاء اليمني</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .danger-button {
            background: #e74c3c;
        }
        .danger-button:hover {
            background: #c0392b;
        }
        .success-button {
            background: #27ae60;
        }
        .success-button:hover {
            background: #229954;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .login-form {
            display: grid;
            gap: 10px;
            max-width: 400px;
        }
        .login-form input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .token-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 اختبار حماية API</h1>
            <h2>نظام إدارة العملاء اليمني</h2>
        </div>

        <div class="test-section">
            <h3>⚠️ اختبار الثغرة الأمنية (قبل الإصلاح)</h3>
            <p>هذا الاختبار يحاول الوصول لـ API المستخدمين بدون أي حماية:</p>
            <button class="test-button danger-button" onclick="testUnprotectedAPI()">
                🚨 اختبار الوصول بدون Token
            </button>
            <div id="unprotected-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔐 تسجيل الدخول للحصول على JWT Token</h3>
            <div class="login-form">
                <input type="text" id="loginName" placeholder="اسم المستخدم" value="admin">
                <input type="password" id="password" placeholder="كلمة المرور" value="admin123456">
                <input type="text" id="deviceId" placeholder="معرف الجهاز" value="test_security_device_123">
                <button class="test-button success-button" onclick="loginAndGetToken()">
                    🔑 تسجيل الدخول
                </button>
            </div>
            <div id="login-result" class="result" style="display: none;"></div>
            <div id="token-display" class="token-display" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>✅ اختبار الوصول مع JWT Token</h3>
            <button class="test-button success-button" onclick="testProtectedAPI()" disabled id="protected-test-btn">
                🔒 اختبار الوصول المحمي
            </button>
            <div id="protected-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>❌ اختبار Token خاطئ</h3>
            <button class="test-button danger-button" onclick="testInvalidToken()">
                🚫 اختبار Token خاطئ
            </button>
            <div id="invalid-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 ملخص النتائج</h3>
            <div id="summary" class="result info" style="display: none;"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8080';
        let currentToken = null;
        let testResults = {
            unprotected: null,
            login: null,
            protected: null,
            invalid: null
        };

        async function testUnprotectedAPI() {
            const resultDiv = document.getElementById('unprotected-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 جاري الاختبار...';
            
            try {
                const response = await fetch(`${BASE_URL}/api/users`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ خطر أمني! API غير محمي
Status: ${response.status}
البيانات المكشوفة: ${JSON.stringify(data, null, 2)}`;
                    testResults.unprotected = false;
                } else {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ممتاز! API محمي بشكل صحيح
Status: ${response.status}
الرسالة: ${data.message || 'غير مصرح بالوصول'}`;
                    testResults.unprotected = true;
                }
            } catch (error) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = `⚠️ خطأ في الاتصال: ${error.message}
تأكد من تشغيل الخادم على ${BASE_URL}`;
                testResults.unprotected = null;
            }
            
            updateSummary();
        }

        async function loginAndGetToken() {
            const resultDiv = document.getElementById('login-result');
            const tokenDiv = document.getElementById('token-display');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 جاري تسجيل الدخول...';
            
            const loginData = {
                loginName: document.getElementById('loginName').value,
                password: document.getElementById('password').value,
                deviceId: document.getElementById('deviceId').value
            };
            
            try {
                const response = await fetch(`${BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                if (response.ok && data.success && data.token) {
                    currentToken = data.token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ تم تسجيل الدخول بنجاح
المستخدم: ${data.user.username}
نوع Token: ${data.tokenType}
صالح لمدة: ${data.expiresIn}`;
                    
                    tokenDiv.style.display = 'block';
                    tokenDiv.innerHTML = `<strong>JWT Token:</strong><br>${currentToken}`;
                    
                    document.getElementById('protected-test-btn').disabled = false;
                    testResults.login = true;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ فشل تسجيل الدخول
Status: ${response.status}
الخطأ: ${data.error || data.message || 'خطأ غير معروف'}`;
                    testResults.login = false;
                }
            } catch (error) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = `⚠️ خطأ في الاتصال: ${error.message}`;
                testResults.login = null;
            }
            
            updateSummary();
        }

        async function testProtectedAPI() {
            if (!currentToken) {
                alert('يجب تسجيل الدخول أولاً للحصول على Token');
                return;
            }
            
            const resultDiv = document.getElementById('protected-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 جاري اختبار الوصول المحمي...';
            
            try {
                const response = await fetch(`${BASE_URL}/api/users`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ تم الوصول بنجاح مع Token صحيح
Status: ${response.status}
عدد المستخدمين: ${data.data?.length || 0}
إجمالي السجلات: ${data.total || 0}`;
                    testResults.protected = true;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ فشل الوصول مع Token
Status: ${response.status}
الخطأ: ${data.message || 'خطأ غير معروف'}`;
                    testResults.protected = false;
                }
            } catch (error) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = `⚠️ خطأ في الاتصال: ${error.message}`;
                testResults.protected = null;
            }
            
            updateSummary();
        }

        async function testInvalidToken() {
            const resultDiv = document.getElementById('invalid-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 جاري اختبار Token خاطئ...';
            
            try {
                const response = await fetch(`${BASE_URL}/api/users`, {
                    headers: {
                        'Authorization': 'Bearer invalid_token_123_fake'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ خطر أمني! تم قبول Token خاطئ
Status: ${response.status}
البيانات: ${JSON.stringify(data, null, 2)}`;
                    testResults.invalid = false;
                } else {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ممتاز! تم رفض Token خاطئ
Status: ${response.status}
الرسالة: ${data.message || 'Token غير صالح'}`;
                    testResults.invalid = true;
                }
            } catch (error) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = `⚠️ خطأ في الاتصال: ${error.message}`;
                testResults.invalid = null;
            }
            
            updateSummary();
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            summaryDiv.style.display = 'block';
            
            let summary = '📊 ملخص نتائج اختبار الحماية:\n\n';
            
            // اختبار الوصول بدون Token
            if (testResults.unprotected === true) {
                summary += '✅ الوصول بدون Token: محمي بشكل صحيح\n';
            } else if (testResults.unprotected === false) {
                summary += '❌ الوصول بدون Token: غير محمي (خطر أمني!)\n';
            } else {
                summary += '⚠️ الوصول بدون Token: لم يتم الاختبار\n';
            }
            
            // اختبار تسجيل الدخول
            if (testResults.login === true) {
                summary += '✅ تسجيل الدخول: يعمل بشكل صحيح\n';
            } else if (testResults.login === false) {
                summary += '❌ تسجيل الدخول: فشل\n';
            } else {
                summary += '⚠️ تسجيل الدخول: لم يتم الاختبار\n';
            }
            
            // اختبار الوصول المحمي
            if (testResults.protected === true) {
                summary += '✅ الوصول مع Token صحيح: يعمل بشكل صحيح\n';
            } else if (testResults.protected === false) {
                summary += '❌ الوصول مع Token صحيح: فشل\n';
            } else {
                summary += '⚠️ الوصول مع Token صحيح: لم يتم الاختبار\n';
            }
            
            // اختبار Token خاطئ
            if (testResults.invalid === true) {
                summary += '✅ رفض Token خاطئ: محمي بشكل صحيح\n';
            } else if (testResults.invalid === false) {
                summary += '❌ رفض Token خاطئ: غير محمي (خطر أمني!)\n';
            } else {
                summary += '⚠️ رفض Token خاطئ: لم يتم الاختبار\n';
            }
            
            // تقييم عام
            const passedTests = Object.values(testResults).filter(r => r === true).length;
            const failedTests = Object.values(testResults).filter(r => r === false).length;
            const totalTests = Object.values(testResults).filter(r => r !== null).length;
            
            summary += `\n📈 النتيجة العامة: ${passedTests}/${totalTests} اختبار نجح\n`;
            
            if (failedTests === 0 && totalTests > 0) {
                summary += '\n🎉 ممتاز! النظام محمي بشكل كامل';
                summaryDiv.className = 'result success';
            } else if (failedTests > 0) {
                summary += '\n⚠️ يوجد مشاكل أمنية تحتاج إصلاح!';
                summaryDiv.className = 'result error';
            } else {
                summary += '\n💡 قم بتشغيل جميع الاختبارات للتأكد من الحماية';
                summaryDiv.className = 'result info';
            }
            
            summaryDiv.textContent = summary;
        }

        // تحديث الملخص عند تحميل الصفحة
        updateSummary();
    </script>
</body>
</html>
