package models

import (
	"time"
)

type Client struct {
	ID         uint      `gorm:"primaryKey"`
	ClientName string    `gorm:"size:100"`
	AppName    string    `gorm:"size:100"`
	CardNumber string    `gorm:"size:50;unique"`
	ClientCode int       `gorm:"unique"`
	Password   string    `gorm:"size:255"`
	IPAddress  string    `gorm:"size:50"`
	Status     int       `gorm:"default:1"` // 1=active, 0=inactive
	UserID     uint      // Foreign key to User
	CreatedAt  time.Time
	UpdatedAt  time.Time
}
