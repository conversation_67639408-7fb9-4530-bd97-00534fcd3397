// اختبار دقيق لـ API المطورين بالبيانات المحددة
const http = require('http');

console.log('🎯 اختبار API المطورين بالبيانات المحددة...\n');

// دالة إرسال طلب API
function sendAPIRequest(testName, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: '***********',
      port: 8080,
      path: '/api/external/verify-direct',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 10000
    };

    console.log(`📡 ${testName}:`);
    console.log(`   البيانات: ${JSON.stringify(data)}`);
    console.log('   ⏳ إرسال الطلب...');

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`   📊 رمز الاستجابة: ${res.statusCode}`);
        
        try {
          const jsonData = JSON.parse(responseData);
          console.log(`   📄 الاستجابة: ${JSON.stringify(jsonData, null, 2)}`);
          
          if (jsonData.status === 'success') {
            console.log('   ✅ نجح الاختبار\n');
          } else {
            console.log(`   ❌ فشل الاختبار: ${jsonData.message}\n`);
          }
          
          resolve({ success: jsonData.status === 'success', data: jsonData });
        } catch (error) {
          console.log(`   ❌ خطأ في تحليل JSON: ${responseData}\n`);
          resolve({ success: false, error: 'Invalid JSON' });
        }
      });
    });

    req.on('error', (error) => {
      console.log(`   ❌ خطأ في الطلب: ${error.message}\n`);
      resolve({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      console.log('   ⏰ انتهت مهلة الطلب\n');
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });

    req.write(postData);
    req.end();
  });
}

// تشغيل الاختبارات
async function runTests() {
  console.log('🚀 بدء الاختبارات المحددة...\n');
  
  // اختبار 1: الوكيل testuser مع العميل النشط 1000
  await sendAPIRequest('اختبار 1 - وكيل testuser + عميل نشط 1000', {
    agent_login_name: 'testuser',
    agent_login_password: 'test123', // قد تحتاج لتعديل كلمة المرور
    client_code: 1000,
    client_token: 'ABC12345' // قد تحتاج لتعديل التوكن
  });

  // اختبار 2: الوكيل testuser مع العميل غير النشط 1005
  await sendAPIRequest('اختبار 2 - وكيل testuser + عميل غير نشط 1005', {
    agent_login_name: 'testuser',
    agent_login_password: 'test123',
    client_code: 1005,
    client_token: 'TEST1005' // قد تحتاج لتعديل التوكن
  });

  // اختبار 3: الوكيل testuser مع العميل غير الموجود 9999
  await sendAPIRequest('اختبار 3 - وكيل testuser + عميل غير موجود 9999', {
    agent_login_name: 'testuser',
    agent_login_password: 'test123',
    client_code: 9999,
    client_token: 'DUMMY999'
  });

  // اختبار 4: وكيل غير موجود
  await sendAPIRequest('اختبار 4 - وكيل غير موجود', {
    agent_login_name: 'nonexistent',
    agent_login_password: 'wrongpass',
    client_code: 1000,
    client_token: 'ABC12345'
  });

  // اختبار 5: كلمة مرور خاطئة للوكيل
  await sendAPIRequest('اختبار 5 - كلمة مرور خاطئة', {
    agent_login_name: 'testuser',
    agent_login_password: 'wrongpassword',
    client_code: 1000,
    client_token: 'ABC12345'
  });

  console.log('🏁 انتهت جميع الاختبارات!');
  console.log('\n💡 ملاحظات:');
  console.log('   - إذا فشلت الاختبارات، تحقق من:');
  console.log('     1. تشغيل الخادم على المنفذ 8080');
  console.log('     2. كلمات المرور الصحيحة في قاعدة البيانات');
  console.log('     3. توكنات العملاء الصحيحة');
  console.log('   - استخدم check-specific-data.js للتحقق من البيانات');
}

runTests();
