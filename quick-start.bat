@echo off
echo 🛑 Stopping all Node.js processes...
taskkill /f /im node.exe 2>nul

echo ⏳ Waiting 2 seconds...
timeout /t 2 /nobreak >nul

echo 🚀 Starting Working Server...
cd /d "C:\yemclinet\server"
echo 📂 Current directory: %cd%
echo 🎯 Running: node working-server.js

start "Yemen Client Server" node working-server.js

echo ✅ Server started in new window
echo 🌐 Server should be running on: http://localhost:8080
echo 🌍 External access: http://***********:8080

pause
