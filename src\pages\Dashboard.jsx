import React from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  Paper
} from '@mui/material'
import {
  People,
  Business,
  SupervisorAccount,
  Security,
  TrendingUp,
  Warning
} from '@mui/icons-material'
import { useQuery } from 'react-query'
import { useAuth } from '../contexts/AuthContext'
import { useSnackbar } from 'notistack'
import LoadingSpinner from '../components/common/LoadingSpinner'

const Dashboard = () => {
  const { api } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  // جلب إحصائيات لوحة التحكم من API
  const { data: stats, isLoading: statsLoading } = useQuery(
    'dashboardStats',
    () => api.get('/api/dashboard/stats').then(res => res.data),
    {
      staleTime: 60000,
      onError: (error) => {
        console.error('Error fetching dashboard stats:', error)
        enqueueSnackbar('خطأ في جلب إحصائيات لوحة التحكم', { variant: 'error' })
      }
    }
  )

  // جلب النشاطات الأخيرة من API
  const { data: recentActivity, isLoading: activityLoading } = useQuery(
    'recentActivity',
    () => api.get('/api/dashboard/recent-activity').then(res => res.data),
    {
      staleTime: 30000,
      onError: (error) => {
        console.error('Error fetching recent activity:', error)
        enqueueSnackbar('خطأ في جلب النشاطات الأخيرة', { variant: 'error' })
      }
    }
  )

  // جلب إحصائيات الأمان من API
  const { data: securityStats, isLoading: securityLoading } = useQuery(
    'securityStats',
    () => api.get('/api/security/stats').then(res => res.data),
    {
      staleTime: 60000,
      onError: (error) => {
        console.error('Error fetching security stats:', error)
        enqueueSnackbar('خطأ في جلب إحصائيات الأمان', { variant: 'error' })
      }
    }
  )

  const statCards = [
    {
      title: 'إجمالي المستخدمين',
      value: stats?.totalUsers || 0,
      subtitle: 'مستخدمي النظام',
      icon: <SupervisorAccount />,
      color: '#667eea',
      progress: 85
    },
    {
      title: 'إجمالي العملاء',
      value: stats?.totalClients || 0,
      subtitle: 'عملاء مسجلين',
      icon: <People />,
      color: '#f093fb',
      progress: 92
    },
    {
      title: 'إجمالي الوكلاء',
      value: stats?.totalAgents || 0,
      subtitle: 'وكلاء نشطين',
      icon: <Business />,
      color: '#4facfe',
      progress: 78
    },
    {
      title: 'سجلات الأمان',
      value: stats?.totalSecurity || 0,
      subtitle: 'محاولات دخول',
      icon: <Security />,
      color: '#764ba2',
      progress: 95
    }
  ]

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        مرحباً بك في لوحة التحكم
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: card.color, mr: 2 }}>
                    {card.icon}
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      {card.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {card.title}
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  {card.subtitle}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={card.progress}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: card.color
                    }
                  }}
                />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* الأنشطة الأخيرة */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                العملاء الجدد
              </Typography>
              <List>
                {recentActivity?.recentClients?.map((client) => (
                  <ListItem key={client.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {client.clientName.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={client.clientName}
                      secondary={`${client.appName} - ${new Date(client.createdAt).toLocaleDateString('ar-SA')}`}
                    />
                    <Chip
                      label={client.status === 1 ? 'مفعل' : 'محظور'}
                      color={client.status === 1 ? 'success' : 'error'}
                      size="small"
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* إحصائيات الأمان */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                تقرير الأمان
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  آخر 24 ساعة
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">محاولات دخول ناجحة</Typography>
                  <Typography variant="body2" color="success.main">
                    {securityStats?.last24Hours?.successfulLogins || 0}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">محاولات دخول فاشلة</Typography>
                  <Typography variant="body2" color="error.main">
                    {securityStats?.last24Hours?.failedAttempts || 0}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">أجهزة فريدة</Typography>
                  <Typography variant="body2">
                    {securityStats?.last24Hours?.uniqueDevices || 0}
                  </Typography>
                </Box>
              </Box>

              {securityStats?.security?.suspiciousIPs?.length > 0 && (
                <Paper sx={{ p: 2, bgcolor: 'error.light', color: 'error.contrastText' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Warning sx={{ mr: 1 }} />
                    <Typography variant="subtitle2">
                      عناوين IP مشبوهة
                    </Typography>
                  </Box>
                  {securityStats.security.suspiciousIPs.map((ip, index) => (
                    <Typography key={index} variant="caption" sx={{ display: 'block' }}>
                      {ip.ipAddress} ({ip.attempts} محاولة)
                    </Typography>
                  ))}
                </Paper>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard
