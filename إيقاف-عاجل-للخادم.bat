@echo off
chcp 65001 >nul
color 0C
echo.
echo ========================================
echo     🚨 إيقاف عاجل للخادم غير المحمي
echo ========================================
echo.

echo ⚠️ تحذير: تم اكتشاف خادم غير محمي يعرض بيانات المستخدمين!
echo.

echo 🛑 إيقاف جميع عمليات Node.js...
taskkill /F /IM node.exe /T >nul 2>&1
if %errorlevel%==0 (
    echo ✅ تم إيقاف عمليات Node.js
) else (
    echo ℹ️ لم توجد عمليات Node.js للإيقاف
)

echo.
echo 🛑 إيقاف جميع العمليات على المنفذ 8080...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
    echo إيقاف العملية %%a...
    taskkill /PID %%a /F >nul 2>&1
    if !errorlevel!==0 (
        echo ✅ تم إيقاف العملية %%a
    )
)

echo.
echo 🛑 إيقاف العمليات على المنافذ الأخرى...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 2^>nul') do (
    taskkill /PID %%a /F >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5173 2^>nul') do (
    taskkill /PID %%a /F >nul 2>&1
)

echo.
echo ⏳ انتظار 5 ثوان للتأكد من إيقاف جميع العمليات...
timeout /t 5 /nobreak >nul

echo.
echo 🔍 فحص المنافذ بعد الإيقاف...
netstat -ano | findstr ":8080\|:3000\|:5173" >nul 2>&1
if %errorlevel%==0 (
    echo ⚠️ لا تزال هناك عمليات تعمل:
    netstat -ano | findstr ":8080\|:3000\|:5173"
    echo.
    echo 🔄 محاولة إيقاف إضافية...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8080\|:3000\|:5173" 2^>nul') do (
        taskkill /PID %%a /F >nul 2>&1
    )
) else (
    echo ✅ تم إيقاف جميع العمليات بنجاح
)

echo.
echo ========================================
echo        🔒 تشغيل الخادم المحمي الآن
echo ========================================
echo.

echo 🚀 تشغيل الخادم المحمي مع JWT Authentication...
echo 📍 الخادم: http://localhost:8080
echo 🌐 خارجي: http://***********:8080
echo.
echo ⚠️ ملاحظة مهمة: جميع /api/* محمية الآن بـ JWT Token
echo ✅ APIs العامة فقط: /api/auth/login, /api/external/health
echo.

cd server
echo 🎯 بدء تشغيل working-server.js المحمي...
echo.

node working-server.js

echo.
echo 🛑 تم إيقاف الخادم
pause
