// اختبار حماية API
const http = require('http');

console.log('🔍 اختبار حماية API');
console.log('==================');

// اختبار API محمي بدون token
function testProtectedAPI() {
  return new Promise((resolve) => {
    console.log('\n🔒 اختبار API محمي بدون token...');
    
    const req = http.request({
      hostname: '***********',
      port: 8080,
      path: '/api/users?page=1&limit=10',
      method: 'GET',
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log(`📊 Status Code: ${res.statusCode}`);
        
        if (res.statusCode === 401) {
          console.log('✅ محمي بشكل صحيح - يرجع 401 Unauthorized');
          try {
            const jsonData = JSON.parse(data);
            console.log('📄 Response:', jsonData.message || jsonData.error);
          } catch (e) {
            console.log('📄 Response:', data.substring(0, 200));
          }
          resolve(true);
        } else if (res.statusCode === 200) {
          console.log('❌ غير محمي - يعرض البيانات!');
          console.log('📄 Response:', data.substring(0, 300));
          resolve(false);
        } else {
          console.log(`⚠️ Status غير متوقع: ${res.statusCode}`);
          console.log('📄 Response:', data.substring(0, 200));
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log('❌ خطأ في الاتصال:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// اختبار API عام للمطورين
function testPublicAPI() {
  return new Promise((resolve) => {
    console.log('\n🌐 اختبار API عام للمطورين...');
    
    const req = http.request({
      hostname: '***********',
      port: 8080,
      path: '/api/external/health',
      method: 'GET',
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log(`📊 Status Code: ${res.statusCode}`);
        
        if (res.statusCode === 200) {
          console.log('✅ API عام يعمل بشكل صحيح');
          try {
            const jsonData = JSON.parse(data);
            console.log('📄 Response:', jsonData.status || jsonData.message);
          } catch (e) {
            console.log('📄 Response:', data.substring(0, 200));
          }
          resolve(true);
        } else {
          console.log(`❌ API عام لا يعمل - Status: ${res.statusCode}`);
          console.log('📄 Response:', data.substring(0, 200));
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log('❌ خطأ في الاتصال:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// تشغيل الاختبارات
async function runTests() {
  console.log('🚀 بدء اختبار الحماية...\n');
  
  const publicWorks = await testPublicAPI();
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const protectedWorks = await testProtectedAPI();
  
  console.log('\n📋 النتائج النهائية:');
  console.log('=================');
  console.log('🌐 API عام للمطورين:', publicWorks ? '✅ يعمل' : '❌ لا يعمل');
  console.log('🔒 حماية APIs الداخلية:', protectedWorks ? '✅ تعمل' : '❌ لا تعمل');
  
  if (publicWorks && protectedWorks) {
    console.log('\n🎉 النظام محمي بشكل مثالي!');
  } else if (!protectedWorks) {
    console.log('\n⚠️ خطر أمني: APIs الداخلية غير محمية!');
  } else {
    console.log('\n❌ يوجد مشاكل في النظام');
  }
}

runTests().catch(console.error);
