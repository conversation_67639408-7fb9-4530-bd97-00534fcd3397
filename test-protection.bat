@echo off
chcp 65001 >nul
echo.
echo ========================================
echo     🧪 اختبار حماية API - نظام إدارة العملاء اليمني
echo ========================================
echo.

echo 🔍 فحص حالة الخادم...
curl -s http://localhost:8080/api/external/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ الخادم غير متاح على http://localhost:8080
    echo 💡 تأكد من تشغيل الخادم أولاً باستخدام: run-protected-server.bat
    pause
    exit /b 1
)

echo ✅ الخادم متاح ويعمل
echo.

echo 🧪 تشغيل اختبار الحماية الشامل...
node test-security-protection.js

echo.
echo 📋 ملخص الاختبار:
echo ==================
echo.
echo 1. اختبار الوصول بدون Token ❌ (يجب أن يفشل)
echo 2. تسجيل الدخول والحصول على JWT Token ✅
echo 3. الوصول مع Token صحيح ✅
echo 4. الوصول مع Token خاطئ ❌ (يجب أن يفشل)
echo 5. اختبار APIs العامة ✅
echo.

pause
