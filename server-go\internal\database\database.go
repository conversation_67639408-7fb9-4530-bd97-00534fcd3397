package database

import (
	"fmt"
	"os"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

// Connect initializes database connection using .env vars
func Connect() (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		os.<PERSON>env("DB_HOST"),
		os.<PERSON>("DB_USER"),
		os.<PERSON>("DB_PASSWORD"),
		os.<PERSON>en<PERSON>("DB_NAME"),
		os.<PERSON>env("DB_PORT"),
	)
	return gorm.Open(postgres.Open(dsn), &gorm.Config{})
}
