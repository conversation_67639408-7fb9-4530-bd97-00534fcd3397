{"name": "yem-client-management", "version": "1.0.0", "description": "نظام إدارة العملاء والوكلاء", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "db:generate": "cd server && npx prisma generate", "db:migrate": "cd server && npx prisma migrate dev", "db:studio": "cd server && npx prisma studio", "test": "node test-remote-agent.js", "test:server": "curl -s http://localhost:8081/api/external/health", "test:api": "node -e \"const axios = require('axios'); axios.post('http://localhost:8081/api/external/verify-direct', {username: 'testuser', password: 'test123', clientCode: 1004, clientToken: 'TEST1004'}, {headers: {'Content-Type': 'application/json'}}).then(r => console.log('✅ API Test Passed:', r.data)).catch(e => console.log('❌ API Test Failed:', e.message))\"", "test:db": "cd server && node -e \"const { PrismaClient } = require('@prisma/client'); const prisma = new PrismaClient(); prisma.user.count().then(count => console.log('✅ Database connected, users:', count)).catch(e => console.log('❌ Database error:', e.message)).finally(() => prisma.$disconnect())\"", "setup": "npm install && cd server && npm install && cd ../client && npm install", "setup:db": "psql -U postgres -c \"CREATE DATABASE yemclient_db;\" && psql -U postgres -d yemclient_db -f create_database.sql"}, "keywords": ["client-management", "agents", "dashboard"], "author": "YemClient Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"axios": "^1.10.0", "node-fetch": "^3.3.2"}}