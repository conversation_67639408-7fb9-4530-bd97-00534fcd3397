const express = require('express');
const path = require('path');

const app = express();
const PORT = 8080;

console.log('🚀 بدء تشغيل الخادم البسيط...');

// تقديم الملفات الثابتة من مجلد client/dist
app.use(express.static(path.join(__dirname, 'client', 'dist')));

// تقديم index.html لجميع المسارات (SPA routing)
app.get('*', (req, res) => {
    console.log('📄 طلب صفحة:', req.path);
    res.sendFile(path.join(__dirname, 'client', 'dist', 'index.html'));
});

app.listen(PORT, () => {
    console.log(`🌟 الخادم يعمل على http://localhost:${PORT}`);
    console.log(`📁 يقدم الملفات من: ${path.join(__dirname, 'client', 'dist')}`);
    console.log('🔧 تم إضافة كود JavaScript لحقل معرف الجهاز 2 مباشرة في index.html');
    console.log('');
    console.log('📋 التعليمات:');
    console.log('1. افتح المتصفح على http://localhost:8080');
    console.log('2. انتقل إلى صفحة إضافة مستخدم أو تعديل مستخدم');
    console.log('3. ستجد حقل "معرف الجهاز 2" تم إضافته تلقائياً');
    console.log('4. افتح Developer Tools (F12) لرؤية رسائل التشخيص');
});
