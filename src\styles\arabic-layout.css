/* تخطيط عربي قوي - إجبار القائمة على اليمين */

/* إجبار اتجاه RTL على كامل التطبيق */
html, body, #root {
  direction: rtl !important;
  text-align: right !important;
}

/* إجبار القائمة الجانبية على اليمين */
.MuiDrawer-root {
  direction: rtl !important;
}

.MuiDrawer-paper {
  direction: rtl !important;
  right: 0 !important;
  left: auto !important;
}

.MuiDrawer-paperAnchorRight {
  right: 0 !important;
  left: auto !important;
}

.MuiDrawer-paperAnchorLeft {
  right: 0 !important;
  left: auto !important;
}

/* إجبار المحتوى الرئيسي على اليسار */
.main-content {
  margin-right: 280px !important;
  margin-left: 0 !important;
  direction: rtl !important;
}

/* إجبار الشريط العلوي */
.MuiAppBar-root {
  direction: rtl !important;
  right: 0 !important;
  left: 0 !important;
}

/* إجبار القوائم */
.MuiList-root {
  direction: rtl !important;
  text-align: right !important;
}

.MuiListItem-root {
  direction: rtl !important;
  text-align: right !important;
}

.MuiListItemText-root {
  text-align: right !important;
}

/* إجبار الأيقونات على اليمين */
.MuiListItemIcon-root {
  min-width: 40px !important;
  margin-left: 16px !important;
  margin-right: 0 !important;
}

/* إجبار الجداول */
.MuiTable-root {
  direction: rtl !important;
}

.MuiTableCell-root {
  text-align: right !important;
}

/* إجبار النماذج */
.MuiTextField-root {
  direction: rtl !important;
}

.MuiInputBase-root {
  direction: rtl !important;
}

/* إجبار الأزرار */
.MuiButton-root {
  direction: rtl !important;
}

/* إجبار البطاقات */
.MuiCard-root {
  direction: rtl !important;
}

.MuiCardContent-root {
  direction: rtl !important;
  text-align: right !important;
}

/* إجبار الحاويات */
.MuiContainer-root {
  direction: rtl !important;
}

.MuiBox-root {
  direction: rtl !important;
}

/* إجبار الشبكة */
.MuiGrid-root {
  direction: rtl !important;
}

/* إجبار النصوص */
.MuiTypography-root {
  direction: rtl !important;
  text-align: right !important;
}

/* إجبار القوائم المنسدلة */
.MuiMenu-root {
  direction: rtl !important;
}

.MuiMenuItem-root {
  direction: rtl !important;
  text-align: right !important;
}

/* إجبار الحوارات */
.MuiDialog-root {
  direction: rtl !important;
}

.MuiDialogContent-root {
  direction: rtl !important;
  text-align: right !important;
}

/* إجبار الأوراق */
.MuiPaper-root {
  direction: rtl !important;
}

/* تخطيط خاص للقائمة الجانبية */
.sidebar-right {
  position: fixed !important;
  right: 0 !important;
  left: auto !important;
  top: 0 !important;
  height: 100vh !important;
  width: 280px !important;
  z-index: 1200 !important;
  direction: rtl !important;
}

/* تخطيط خاص للمحتوى */
.content-with-sidebar {
  margin-right: 280px !important;
  margin-left: 0 !important;
  direction: rtl !important;
  min-height: 100vh !important;
}

/* إخفاء scrollbar الأفقي */
body {
  overflow-x: hidden !important;
}

/* تأكيد اتجاه RTL لكل شيء */
* {
  direction: rtl !important;
}

/* استثناءات للعناصر التي تحتاج LTR */
.ltr-element {
  direction: ltr !important;
}

/* تصحيح موقع الأيقونات في القائمة */
.menu-icon {
  margin-left: 12px !important;
  margin-right: 0 !important;
}

/* تصحيح موقع النصوص في القائمة */
.menu-text {
  text-align: right !important;
  direction: rtl !important;
}

/* إجبار الشريط العلوي على التمدد الكامل */
.top-bar {
  width: 100% !important;
  right: 0 !important;
  left: 0 !important;
}

/* إجبار المحتوى على عدم التداخل مع القائمة */
.main-content-area {
  padding-right: 300px !important;
  padding-left: 20px !important;
  direction: rtl !important;
}

@media (max-width: 600px) {
  .main-content-area {
    padding-right: 20px !important;
    padding-left: 20px !important;
  }
  
  .content-with-sidebar {
    margin-right: 0 !important;
  }
}
