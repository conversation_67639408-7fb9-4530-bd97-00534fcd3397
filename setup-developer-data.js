// إعداد البيانات التجريبية للمطورين
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function setupDeveloperData() {
  try {
    console.log('🚀 إعداد البيانات التجريبية للمطورين...\n');

    // 1. إنشاء الوكيل التجريبي
    console.log('1️⃣ إنشاء الوكيل التجريبي...');
    
    let testAgent = await prisma.agent.findFirst({
      where: { loginName: 'testuser' }
    });

    if (!testAgent) {
      const hashedPassword = await bcrypt.hash('test123', 10);
      
      testAgent = await prisma.agent.create({
        data: {
          agentName: 'وكيل تجريبي للمطورين',
          agencyName: 'وكالة تجريبية',
          agencyType: 'وكالة اختبار',
          ipAddress: '*************',
          loginName: 'testuser',
          loginPassword: hashedPassword,
          isActive: true
        }
      });
      console.log('✅ تم إنشاء الوكيل التجريبي');
    } else {
      console.log('✅ الوكيل التجريبي موجود بالفعل');
    }

    // 2. إنشاء العملاء التجريبيين
    console.log('\n2️⃣ إنشاء العملاء التجريبيين...');
    
    const testClients = [
      {
        clientCode: 1004,
        clientName: 'عميل تجريبي نشط',
        appName: 'تطبيق تجريبي',
        cardNumber: '10040001',
        token: 'TEST1004',
        password: await bcrypt.hash('TEST1004', 10),
        ipAddress: '*************',
        status: 1
      },
      {
        clientCode: 1005,
        clientName: 'عميل تجريبي غير نشط',
        appName: 'تطبيق تجريبي 2',
        cardNumber: '10050001',
        token: 'TEST1005',
        password: await bcrypt.hash('TEST1005', 10),
        ipAddress: '*************',
        status: 0
      },
      {
        clientCode: 9999,
        clientName: 'عميل وهمي للاختبار',
        appName: 'تطبيق وهمي',
        cardNumber: '99990001',
        token: 'DUMMY999',
        password: await bcrypt.hash('DUMMY999', 10),
        ipAddress: '*************',
        status: 1
      },
      {
        clientCode: 1000,
        clientName: 'عميل آخر للاختبار',
        appName: 'تطبيق آخر',
        cardNumber: '10000001',
        token: 'ABC12345',
        password: await bcrypt.hash('ABC12345', 10),
        ipAddress: '*************',
        status: 1
      }
    ];

    for (const clientData of testClients) {
      const existingClient = await prisma.client.findFirst({
        where: { clientCode: clientData.clientCode }
      });

      if (!existingClient) {
        await prisma.client.create({ data: clientData });
        console.log(`✅ تم إنشاء العميل ${clientData.clientCode}`);
      } else {
        // تحديث التوكن والكلمة السرية
        await prisma.client.update({
          where: { id: existingClient.id },
          data: {
            token: clientData.token,
            password: clientData.password,
            status: clientData.status
          }
        });
        console.log(`✅ تم تحديث العميل ${clientData.clientCode}`);
      }
    }

    console.log('\n🎉 تم إعداد جميع البيانات التجريبية بنجاح!');
    console.log('\n📋 بيانات الاختبار:');
    console.log('====================');
    console.log('🔐 الوكيل التجريبي:');
    console.log('   اسم المستخدم: testuser');
    console.log('   كلمة المرور: test123');
    console.log('\n💼 العملاء التجريبيين:');
    console.log('   العميل 1004: TEST1004 (نشط)');
    console.log('   العميل 1005: TEST1005 (غير نشط)');
    console.log('   العميل 9999: DUMMY999 (نشط)');
    console.log('   العميل 1000: ABC12345 (نشط)');

    console.log('\n🌐 رابط API للاختبار:');
    console.log('   http://185.11.8.26:8080/api/external/verify-direct');

  } catch (error) {
    console.error('❌ خطأ في إعداد البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupDeveloperData();
