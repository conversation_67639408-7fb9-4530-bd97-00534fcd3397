<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المطورين - دقيق ومضبوط</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border-right: 5px solid #3498db;
        }
        
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success { background: linear-gradient(45deg, #27ae60, #2ecc71); }
        .btn-warning { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .quick-test {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .quick-test:hover {
            border-color: #3498db;
            transform: translateY(-5px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        .status-info { background: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 اختبار API المطورين</h1>
            <p>اختبار دقيق ومضبوط لآلية ربط المطورين</p>
        </div>
        
        <div class="content">
            <!-- إعدادات الخادم -->
            <div class="test-section">
                <h3>⚙️ إعدادات الخادم</h3>
                <p>الخادم الحالي: <strong id="currentServer">محلي</strong></p>
                <button class="btn btn-warning" onclick="switchServer()">🔄 تبديل الخادم</button>
                <button class="btn" onclick="testConnection()">🔗 اختبار الاتصال</button>
                <div style="margin-top: 10px; font-size: 14px; color: #666;">
                    <p>📍 الخادم المحلي: http://localhost:8080</p>
                    <p>🌐 الخادم الخارجي: http://***********:8080</p>
                </div>
            </div>

            <!-- اختبار مخصص -->
            <div class="test-section">
                <h3>🔧 اختبار مخصص</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div class="form-group">
                        <label>اسم دخول الوكيل:</label>
                        <input type="text" id="agentName" value="testuser" placeholder="testuser">
                    </div>
                    <div class="form-group">
                        <label>كلمة مرور الوكيل:</label>
                        <input type="password" id="agentPassword" value="test123" placeholder="test123">
                    </div>
                    <div class="form-group">
                        <label>رمز العميل:</label>
                        <input type="number" id="clientCode" value="1000" placeholder="1000">
                    </div>
                    <div class="form-group">
                        <label>توكن العميل:</label>
                        <input type="text" id="clientToken" value="ABC12345" placeholder="ABC12345">
                    </div>
                </div>
                <button class="btn" onclick="testCustom()">🧪 اختبار مخصص</button>
                <button class="btn btn-warning" onclick="clearResults()">🗑️ مسح النتائج</button>
            </div>
            
            <!-- الاختبارات السريعة -->
            <div class="test-section">
                <h3>⚡ الاختبارات السريعة المحددة</h3>
                <div class="quick-tests">
                    <div class="quick-test">
                        <h4>✅ عميل نشط</h4>
                        <p>testuser + عميل 1000</p>
                        <button class="btn btn-success" onclick="testActiveClient()">اختبار</button>
                    </div>
                    <div class="quick-test">
                        <h4>⚠️ عميل غير نشط</h4>
                        <p>testuser + عميل 1005</p>
                        <button class="btn btn-warning" onclick="testInactiveClient()">اختبار</button>
                    </div>
                    <div class="quick-test">
                        <h4>❌ عميل غير موجود</h4>
                        <p>testuser + عميل 9999</p>
                        <button class="btn btn-danger" onclick="testNonExistentClient()">اختبار</button>
                    </div>
                    <div class="quick-test">
                        <h4>❌ وكيل خطأ</h4>
                        <p>وكيل غير موجود</p>
                        <button class="btn btn-danger" onclick="testWrongAgent()">اختبار</button>
                    </div>
                </div>
            </div>
            
            <!-- اختبار شامل -->
            <div class="test-section">
                <h3>🚀 اختبار شامل</h3>
                <p>تشغيل جميع السيناريوهات المحددة تلقائياً</p>
                <button class="btn" onclick="runAllTests()" style="font-size: 18px; padding: 15px 30px;">
                    🎯 تشغيل جميع الاختبارات
                </button>
            </div>
            
            <!-- النتائج -->
            <div id="results"></div>
        </div>
    </div>

    <script>
        // دعم كلاً من الخادم المحلي والخارجي
        let API_URL = 'http://localhost:8080/api/external/verify-direct';
        let CURRENT_SERVER = 'محلي';

        // تبديل الخادم
        function switchServer() {
            if (API_URL.includes('localhost')) {
                API_URL = 'http://***********:8080/api/external/verify-direct';
                CURRENT_SERVER = 'خارجي';
            } else {
                API_URL = 'http://localhost:8080/api/external/verify-direct';
                CURRENT_SERVER = 'محلي';
            }

            document.getElementById('currentServer').textContent = CURRENT_SERVER;
            addResult('🔄 تم تبديل الخادم', {
                current_server: CURRENT_SERVER,
                api_url: API_URL
            }, 'info');
        }

        // اختبار الاتصال بالخادم
        async function testConnection() {
            const healthURL = API_URL.replace('/api/external/verify-direct', '/api/external/health');

            try {
                addResult('🔗 اختبار الاتصال', {
                    server: CURRENT_SERVER,
                    url: healthURL
                }, 'info');

                const response = await fetch(healthURL, {
                    method: 'GET',
                    timeout: 5000
                });

                const data = await response.json();

                addResult('✅ الاتصال ناجح', {
                    server: CURRENT_SERVER,
                    status: response.status,
                    response: data
                }, 'success');

            } catch (error) {
                addResult('❌ فشل الاتصال', {
                    server: CURRENT_SERVER,
                    error: error.message,
                    suggestion: CURRENT_SERVER === 'محلي' ?
                        'تأكد من تشغيل الخادم: cd server && node working-server.js' :
                        'تأكد من أن الخادم الخارجي يعمل أو جرب الخادم المحلي'
                }, 'error');
            }
        }
        
        function addResult(title, data, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            
            const timestamp = new Date().toLocaleString('ar-SA');
            resultDiv.innerHTML = `
                <strong>${title}</strong> - ${timestamp}
                <span class="status-indicator status-${type}"></span>
                
${JSON.stringify(data, null, 2)}
            `;
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function sendAPIRequest(title, requestData) {
            try {
                addResult(`📡 ${title} - إرسال الطلب`, requestData, 'info');
                
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const responseData = await response.json();
                
                let resultType = 'info';
                if (responseData.status === 'success') {
                    resultType = 'success';
                } else if (responseData.status === 'client_inactive') {
                    resultType = 'warning';
                } else {
                    resultType = 'error';
                }
                
                addResult(`📄 ${title} - الاستجابة`, {
                    status_code: response.status,
                    response: responseData
                }, resultType);
                
                return responseData;
                
            } catch (error) {
                addResult(`❌ ${title} - خطأ`, {
                    error: error.message,
                    note: 'تأكد من تشغيل الخادم على المنفذ 8080'
                }, 'error');
                return null;
            }
        }
        
        function testCustom() {
            const agentName = document.getElementById('agentName').value;
            const agentPassword = document.getElementById('agentPassword').value;
            const clientCode = parseInt(document.getElementById('clientCode').value);
            const clientToken = document.getElementById('clientToken').value;
            
            if (!agentName || !agentPassword || !clientCode || !clientToken) {
                alert('يرجى ملء جميع الحقول');
                return;
            }
            
            sendAPIRequest('اختبار مخصص', {
                agent_login_name: agentName,
                agent_login_password: agentPassword,
                client_code: clientCode,
                client_token: clientToken
            });
        }
        
        function testActiveClient() {
            sendAPIRequest('عميل نشط (1000)', {
                agent_login_name: 'testuser',
                agent_login_password: 'test123',
                client_code: 1000,
                client_token: 'ABC12345'
            });
        }
        
        function testInactiveClient() {
            sendAPIRequest('عميل غير نشط (1005)', {
                agent_login_name: 'testuser',
                agent_login_password: 'test123',
                client_code: 1005,
                client_token: 'TEST1005'
            });
        }
        
        function testNonExistentClient() {
            sendAPIRequest('عميل غير موجود (9999)', {
                agent_login_name: 'testuser',
                agent_login_password: 'test123',
                client_code: 9999,
                client_token: 'DUMMY999'
            });
        }
        
        function testWrongAgent() {
            sendAPIRequest('وكيل غير موجود', {
                agent_login_name: 'nonexistent',
                agent_login_password: 'wrongpass',
                client_code: 1000,
                client_token: 'ABC12345'
            });
        }
        
        async function runAllTests() {
            addResult('🚀 بدء الاختبار الشامل', {
                message: 'تشغيل جميع السيناريوهات المحددة...',
                scenarios: [
                    'testuser + عميل نشط 1000',
                    'testuser + عميل غير نشط 1005',
                    'testuser + عميل غير موجود 9999',
                    'وكيل غير موجود',
                    'كلمة مرور خاطئة'
                ]
            }, 'info');
            
            // تأخير بين الاختبارات
            await new Promise(resolve => setTimeout(resolve, 1000));
            testActiveClient();
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            testInactiveClient();
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            testNonExistentClient();
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            testWrongAgent();
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            sendAPIRequest('كلمة مرور خاطئة', {
                agent_login_name: 'testuser',
                agent_login_password: 'wrongpassword',
                client_code: 1000,
                client_token: 'ABC12345'
            });
            
            setTimeout(() => {
                addResult('🏁 انتهى الاختبار الشامل', {
                    message: 'تم تشغيل جميع السيناريوهات',
                    note: 'راجع النتائج أعلاه لمعرفة حالة كل اختبار'
                }, 'success');
            }, 10000);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // اختبار الاتصال عند تحميل الصفحة
        window.onload = function() {
            document.getElementById('currentServer').textContent = CURRENT_SERVER;

            addResult('🌐 مرحباً بك في اختبار API المطورين', {
                current_server: CURRENT_SERVER,
                api_url: API_URL,
                test_agent: 'testuser',
                test_clients: [1000, 1005, 9999],
                note: 'استخدم "اختبار الاتصال" للتأكد من عمل الخادم'
            }, 'info');

            // اختبار الاتصال تلقائياً
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
