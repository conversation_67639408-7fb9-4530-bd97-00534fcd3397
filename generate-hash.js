// إنشاء hash لكلمة المرور hash8080
const bcrypt = require('bcrypt');

async function generateHash() {
  const password = 'hash8080';
  const saltRounds = 10;
  
  console.log('🔐 إنشاء hash لكلمة المرور:', password);
  
  try {
    const hash = await bcrypt.hash(password, saltRounds);
    console.log('✅ Hash تم إنشاؤه بنجاح:');
    console.log(hash);
    
    // اختبار الـ hash
    const isValid = await bcrypt.compare(password, hash);
    console.log('🧪 اختبار الـ hash:', isValid ? '✅ صحيح' : '❌ خطأ');
    
    console.log('\n📋 استخدم هذا الـ hash في SQL:');
    console.log(`UPDATE users SET password = '${hash}' WHERE login_name = 'hash8080';`);
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء الـ hash:', error);
  }
}

generateHash();
