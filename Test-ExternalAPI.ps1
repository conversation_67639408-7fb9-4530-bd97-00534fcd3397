# 🔒 اختبار حماية API على العنوان الخارجي
# Test External API Protection

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "     🔒 اختبار حماية API على العنوان الخارجي" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$baseUrl = "http://***********:8080"
$apiUrl = "$baseUrl/api/users"
$loginUrl = "$baseUrl/api/auth/login"

Write-Host "🎯 اختبار العنوان: $apiUrl" -ForegroundColor White
Write-Host ""

# اختبار 1: محاولة الوصول بدون Token
Write-Host "🧪 اختبار 1: محاولة الوصول بدون Token..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Gray
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $apiUrl -Method Get -TimeoutSec 10
    Write-Host "❌ خطر أمني! API غير محمي - تم الوصول بدون Token" -ForegroundColor Red
    Write-Host "البيانات المكشوفة:" -ForegroundColor Red
    $response | ConvertTo-Json -Depth 2 | Write-Host -ForegroundColor Red
    $test1Result = "FAILED"
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ ممتاز! API محمي - تم رفض الوصول بدون Token" -ForegroundColor Green
        Write-Host "Status: 401 Unauthorized" -ForegroundColor Green
        $test1Result = "PASSED"
    } else {
        Write-Host "⚠️ خطأ في الاتصال: $($_.Exception.Message)" -ForegroundColor Yellow
        $test1Result = "ERROR"
    }
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Gray
Write-Host ""

# اختبار 2: تسجيل الدخول للحصول على Token
Write-Host "🧪 اختبار 2: تسجيل الدخول للحصول على Token..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Gray
Write-Host ""

$loginData = @{
    loginName = "admin"
    password = "admin123456"
    deviceId = "test_external_device"
} | ConvertTo-Json

$token = $null

try {
    $response = Invoke-RestMethod -Uri $loginUrl -Method Post -Body $loginData -ContentType "application/json" -TimeoutSec 10
    
    if ($response.success -and $response.token) {
        Write-Host "✅ تم تسجيل الدخول بنجاح" -ForegroundColor Green
        Write-Host "المستخدم: $($response.user.username)" -ForegroundColor Green
        Write-Host "Token: $($response.token.Substring(0, 50))..." -ForegroundColor Green
        $token = $response.token
        $test2Result = "PASSED"
    } else {
        Write-Host "❌ فشل تسجيل الدخول" -ForegroundColor Red
        Write-Host "الخطأ: $($response.error)" -ForegroundColor Red
        $test2Result = "FAILED"
    }
} catch {
    Write-Host "❌ خطأ في تسجيل الدخول: $($_.Exception.Message)" -ForegroundColor Red
    $test2Result = "ERROR"
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Gray
Write-Host ""

# اختبار 3: الوصول مع Token صحيح
Write-Host "🧪 اختبار 3: الوصول مع Token صحيح..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Gray
Write-Host ""

if ($token) {
    try {
        $headers = @{
            Authorization = "Bearer $token"
        }
        
        $response = Invoke-RestMethod -Uri $apiUrl -Method Get -Headers $headers -TimeoutSec 10
        Write-Host "✅ تم الوصول بنجاح مع Token صحيح" -ForegroundColor Green
        Write-Host "عدد المستخدمين: $($response.data.Count)" -ForegroundColor Green
        Write-Host "إجمالي السجلات: $($response.total)" -ForegroundColor Green
        $test3Result = "PASSED"
    } catch {
        Write-Host "❌ فشل الوصول مع Token صحيح: $($_.Exception.Message)" -ForegroundColor Red
        $test3Result = "FAILED"
    }
} else {
    Write-Host "❌ لا يوجد Token للاختبار" -ForegroundColor Red
    $test3Result = "SKIPPED"
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Gray
Write-Host ""

# اختبار 4: الوصول مع Token خاطئ
Write-Host "🧪 اختبار 4: الوصول مع Token خاطئ..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Gray
Write-Host ""

try {
    $headers = @{
        Authorization = "Bearer invalid_fake_token_123456789"
    }
    
    $response = Invoke-RestMethod -Uri $apiUrl -Method Get -Headers $headers -TimeoutSec 10
    Write-Host "❌ خطر أمني! تم قبول Token خاطئ" -ForegroundColor Red
    Write-Host "البيانات: $($response | ConvertTo-Json -Depth 1)" -ForegroundColor Red
    $test4Result = "FAILED"
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ ممتاز! تم رفض Token خاطئ" -ForegroundColor Green
        Write-Host "Status: 401 Unauthorized" -ForegroundColor Green
        $test4Result = "PASSED"
    } else {
        Write-Host "⚠️ خطأ غير متوقع: $($_.Exception.Message)" -ForegroundColor Yellow
        $test4Result = "ERROR"
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           📊 ملخص النتائج" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# عرض النتائج
Write-Host "📋 نتائج الاختبارات:" -ForegroundColor White
Write-Host ""

if ($test1Result -eq "PASSED") {
    Write-Host "✅ الوصول بدون Token: محمي بشكل صحيح" -ForegroundColor Green
} elseif ($test1Result -eq "FAILED") {
    Write-Host "❌ الوصول بدون Token: غير محمي (خطر أمني!)" -ForegroundColor Red
} else {
    Write-Host "⚠️ الوصول بدون Token: خطأ في الاختبار" -ForegroundColor Yellow
}

if ($test2Result -eq "PASSED") {
    Write-Host "✅ تسجيل الدخول: يعمل بشكل صحيح" -ForegroundColor Green
} elseif ($test2Result -eq "FAILED") {
    Write-Host "❌ تسجيل الدخول: فشل" -ForegroundColor Red
} else {
    Write-Host "⚠️ تسجيل الدخول: خطأ في الاختبار" -ForegroundColor Yellow
}

if ($test3Result -eq "PASSED") {
    Write-Host "✅ الوصول مع Token صحيح: يعمل بشكل صحيح" -ForegroundColor Green
} elseif ($test3Result -eq "FAILED") {
    Write-Host "❌ الوصول مع Token صحيح: فشل" -ForegroundColor Red
} elseif ($test3Result -eq "SKIPPED") {
    Write-Host "⚠️ الوصول مع Token صحيح: تم تخطيه (لا يوجد token)" -ForegroundColor Yellow
} else {
    Write-Host "⚠️ الوصول مع Token صحيح: خطأ في الاختبار" -ForegroundColor Yellow
}

if ($test4Result -eq "PASSED") {
    Write-Host "✅ رفض Token خاطئ: محمي بشكل صحيح" -ForegroundColor Green
} elseif ($test4Result -eq "FAILED") {
    Write-Host "❌ رفض Token خاطئ: غير محمي (خطر أمني!)" -ForegroundColor Red
} else {
    Write-Host "⚠️ رفض Token خاطئ: خطأ في الاختبار" -ForegroundColor Yellow
}

Write-Host ""

# تقييم عام
$passedTests = @($test1Result, $test2Result, $test3Result, $test4Result) | Where-Object { $_ -eq "PASSED" }
$failedTests = @($test1Result, $test2Result, $test3Result, $test4Result) | Where-Object { $_ -eq "FAILED" }

Write-Host "📈 النتيجة العامة: $($passedTests.Count)/4 اختبار نجح" -ForegroundColor White
Write-Host ""

if ($failedTests.Count -eq 0 -and $passedTests.Count -gt 0) {
    Write-Host "🎉 ممتاز! النظام محمي بشكل كامل على العنوان الخارجي" -ForegroundColor Green
    Write-Host "✅ $baseUrl آمن ومحمي" -ForegroundColor Green
} elseif ($failedTests.Count -gt 0) {
    Write-Host "⚠️ يوجد مشاكل أمنية تحتاج إصلاح!" -ForegroundColor Red
    Write-Host "❌ $baseUrl غير آمن" -ForegroundColor Red
} else {
    Write-Host "💡 لم يتم إجراء اختبارات كافية" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "💡 إذا كان النظام غير محمي:" -ForegroundColor Yellow
Write-Host "   1. تأكد من تشغيل الخادم المحمي: run-protected-server.bat" -ForegroundColor White
Write-Host "   2. تحقق من أن working-server.js يحتوي على JWT protection" -ForegroundColor White
Write-Host "   3. أعد تشغيل الخادم بعد التعديلات" -ForegroundColor White
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Read-Host "اضغط Enter للإغلاق"
