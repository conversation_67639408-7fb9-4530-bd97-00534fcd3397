#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import os
import sys
from pathlib import Path

# إعداد المنفذ
PORT = 8080

# تحديد مجلد الملفات الثابتة
STATIC_DIR = Path(__file__).parent / "client" / "dist"

print("🚀 بدء تشغيل خادم Python HTTP...")
print(f"📁 مجلد الملفات: {STATIC_DIR}")
print(f"🌐 الرابط: http://localhost:{PORT}")
print("")

# التحقق من وجود المجلد
if not STATIC_DIR.exists():
    print(f"❌ المجلد غير موجود: {STATIC_DIR}")
    print("تأكد من وجود مجلد client/dist")
    input("اضغط Enter للخروج...")
    sys.exit(1)

# تغيير المجلد الحالي
os.chdir(STATIC_DIR)

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # إضافة headers للتعامل مع SPA
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_GET(self):
        # إذا كان الملف غير موجود، أرسل index.html (SPA routing)
        if not os.path.exists(self.path.lstrip('/')):
            if not self.path.startswith('/assets/'):
                self.path = '/index.html'
        
        return super().do_GET()

try:
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print("✅ الخادم يعمل بنجاح!")
        print("🔧 تم إضافة كود JavaScript لحقل معرف الجهاز 2")
        print("")
        print("📋 التعليمات:")
        print("1. افتح المتصفح على http://localhost:8080")
        print("2. انتقل إلى صفحة إضافة مستخدم أو تعديل مستخدم")
        print("3. ستجد حقل 'معرف الجهاز 2' تم إضافته تلقائياً")
        print("4. اضغط Ctrl+C لإيقاف الخادم")
        print("")
        
        httpd.serve_forever()
        
except KeyboardInterrupt:
    print("\n🛑 تم إيقاف الخادم")
except Exception as e:
    print(f"❌ خطأ في تشغيل الخادم: {e}")
    input("اضغط Enter للخروج...")
