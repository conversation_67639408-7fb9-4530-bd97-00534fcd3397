const axios = require('axios');
const crypto = require('crypto');

// إعدادات الاختبار
const API_BASE_URL = 'http://***********:8080';
const TEST_AGENT = {
  login_name: 'testuser',
  password: 'test123'
};

const TEST_CLIENTS = [
  { code: 1000, token: 'ABC12345', expected_status: 1, description: 'Active Client 1000' },
  { code: 1004, token: 'TEST1004', expected_status: 1, description: 'Active Client 1004' },
  { code: 1005, token: 'TEST1005', expected_status: 0, description: 'Inactive Client 1005' },
  { code: 9999, token: 'DEMO999', expected_status: 1, description: 'Active Client 9999' }
];

// دالة تحويل التوكن إلى MD5
function toMD5(text) {
  return crypto.createHash('md5').update(text).digest('hex');
}

// دالة اختبار API
async function testDeveloperAPI() {
  console.log('🧪 بدء اختبار API المطورين...\n');

  for (const client of TEST_CLIENTS) {
    try {
      console.log(`📋 اختبار: ${client.description}`);
      console.log(`   رمز العميل: ${client.code}`);
      console.log(`   التوكن الأصلي: ${client.token}`);
      
      const tokenMD5 = toMD5(client.token);
      console.log(`   التوكن MD5: ${tokenMD5}`);

      const requestData = {
        agent_login_name: TEST_AGENT.login_name,
        agent_login_password: TEST_AGENT.password,
        client_code: client.code,
        client_token: tokenMD5
      };

      console.log(`   📤 إرسال الطلب...`);
      
      const response = await axios.post(`${API_BASE_URL}/api/external/verify-direct`, requestData, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Developer-Test-Tool/1.0'
        }
      });

      console.log(`   ✅ الاستجابة: HTTP ${response.status}`);
      console.log(`   📊 البيانات:`, response.data);
      
      // التحقق من النتيجة المتوقعة
      if (response.data.status === 'success' && response.data.client_status === client.expected_status) {
        console.log(`   🎯 النتيجة صحيحة: client_status = ${client.expected_status}`);
      } else {
        console.log(`   ⚠️ النتيجة غير متوقعة: توقع ${client.expected_status}, حصل على ${response.data.client_status}`);
      }

    } catch (error) {
      console.log(`   ❌ خطأ في الاختبار:`);
      if (error.response) {
        console.log(`      HTTP Status: ${error.response.status}`);
        console.log(`      Response:`, error.response.data);
      } else if (error.request) {
        console.log(`      لا يوجد رد من الخادم`);
        console.log(`      Error:`, error.message);
      } else {
        console.log(`      خطأ في الإعداد:`, error.message);
      }
    }
    
    console.log(''); // سطر فارغ
  }

  // اختبار حالات الخطأ
  console.log('🔍 اختبار حالات الخطأ...\n');

  // اختبار وكيل غير موجود
  try {
    console.log('📋 اختبار: وكيل غير موجود');
    const response = await axios.post(`${API_BASE_URL}/api/external/verify-direct`, {
      agent_login_name: 'nonexistent',
      agent_login_password: 'wrong',
      client_code: 1000,
      client_token: toMD5('ABC12345')
    });
    console.log('   ⚠️ توقع خطأ ولكن حصل على:', response.data);
  } catch (error) {
    if (error.response && error.response.data.status === 'agent_error') {
      console.log('   ✅ خطأ الوكيل تم اكتشافه بشكل صحيح');
    } else {
      console.log('   ❌ خطأ غير متوقع:', error.response?.data || error.message);
    }
  }

  // اختبار عميل غير موجود
  try {
    console.log('\n📋 اختبار: عميل غير موجود');
    const response = await axios.post(`${API_BASE_URL}/api/external/verify-direct`, {
      agent_login_name: TEST_AGENT.login_name,
      agent_login_password: TEST_AGENT.password,
      client_code: 99999,
      client_token: toMD5('NONEXISTENT')
    });
    console.log('   ⚠️ توقع خطأ ولكن حصل على:', response.data);
  } catch (error) {
    if (error.response && error.response.data.status === 'client_error') {
      console.log('   ✅ خطأ العميل تم اكتشافه بشكل صحيح');
    } else {
      console.log('   ❌ خطأ غير متوقع:', error.response?.data || error.message);
    }
  }

  console.log('\n🏁 انتهى الاختبار');
}

// تشغيل الاختبار
if (require.main === module) {
  testDeveloperAPI().catch(console.error);
}

module.exports = { testDeveloperAPI, toMD5 };
