# 🚀 تعليمات تشغيل خادم إدارة العملاء اليمني

## ⚡ التشغيل السريع

### الطريقة الأولى (الأسهل):
1. **انقر مرتين** على ملف `تشغيل-الخادم.bat`
2. انتظر حتى تظهر رسالة "Server running on port 8080"

### الطريقة الثانية (يدوياً):
1. افتح **Command Prompt** أو **PowerShell**
2. انتقل إلى مجلد المشروع: `cd C:\yemclinet`
3. شغل الأمر: `node server/working-server.js`

---

## 🔍 التحقق من عمل الخادم

بعد تشغيل الخادم، افتح المتصفح واذهب إلى:

### 🌐 للواجهة الرئيسية:
```
http://localhost:8080
```

### 🔗 لـ API المطورين:
```
http://***********:8080/api/external/verify-direct
```

### ✅ لفحص حالة الخادم:
```
http://***********:8080/api/external/health
```

---

## 🧪 اختبار API المطورين

### النموذج الجديد (مُحسن):
```json
POST http://***********:8080/api/external/verify-direct
Content-Type: application/json

{
  "username": "testuser",
  "password": "test123",
  "clientCode": 1004,
  "clientToken": "TEST1004",
  "deviceId": "DEV001"
}
```

### النموذج القديم (للتوافق):
```json
POST http://***********:8080/api/external/verify-direct
Content-Type: application/json

{
  "agent_login_name": "testuser",
  "agent_login_password": "test123",
  "client_code": 1004,
  "client_token": "TEST1004"
}
```

---

## 📋 بيانات الاختبار

| النوع | المعرف | كلمة المرور/التوكن | الحالة |
|-------|--------|-------------------|--------|
| وكيل اختبار | testuser | test123 | نشط |
| عميل 1 | 1004 | TEST1004 | نشط |
| عميل 2 | 1005 | TEST1005 | غير نشط |
| عميل 3 | 9999 | DUMMY999 | نشط |

---

## 🛠️ استكشاف الأخطاء

### إذا لم يعمل الخادم:
1. تأكد من تثبيت **Node.js**
2. تأكد من وجود ملف `server/working-server.js`
3. تحقق من أن المنفذ 8080 غير مستخدم

### إذا ظهر خطأ "API not found":
1. تأكد من تشغيل الخادم أولاً
2. تحقق من الرابط: `http://***********:8080/api/external/health`
3. جرب الرابط المحلي: `http://localhost:8080/api/external/health`

### للحصول على المساعدة:
- افتح `test-server-simple.html` في المتصفح لاختبار شامل
- تحقق من سجلات الخادم في نافذة الأوامر

---

## 🔒 الحماية

✅ **تم تطبيق الحماية الكاملة:**
- جميع APIs الداخلية محمية بـ JWT Token
- APIs المطورين الخارجية متاحة بدون token
- فحص صحة بيانات الوكيل والعميل
- تسجيل جميع العمليات في قاعدة البيانات

---

## 📞 الدعم الفني

إذا واجهت أي مشاكل، تأكد من:
1. ✅ تشغيل الخادم بنجاح
2. ✅ الاتصال بقاعدة البيانات
3. ✅ صحة بيانات الاختبار
4. ✅ عدم حجب الجدار الناري للمنفذ 8080
