@echo off
chcp 65001 >nul
color 0A
title إعادة تشغيل الخادم المحمي

echo.
echo ========================================
echo     🔄 إعادة تشغيل الخادم المحمي
echo ========================================
echo.

echo 🛑 إيقاف الخادم الحالي...
taskkill /F /IM node.exe /T 2>nul
if %errorlevel%==0 (
    echo ✅ تم إيقاف الخادم
) else (
    echo ℹ️ لم يكن هناك خادم يعمل
)

echo.
echo ⏳ انتظار 2 ثانية...
timeout /t 2 /nobreak >nul

echo.
echo 🚀 تشغيل الخادم المحمي المحدث...
echo ================================================
echo 🛡️ مع حماية JWT كاملة
echo 🌐 APIs عامة: /api/auth/login, /external/verify-direct
echo 📍 http://185.11.8.26:8080
echo.

cd server
echo 🎯 بدء تشغيل working-server.js...
echo.

node working-server.js

echo.
echo 🛑 تم إيقاف الخادم
pause
