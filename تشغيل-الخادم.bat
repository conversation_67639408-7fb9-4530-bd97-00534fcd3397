@echo off
chcp 65001 >nul
echo.
echo ========================================
echo        تشغيل خادم إدارة العملاء اليمني
echo ========================================
echo.

echo 🛑 إيقاف أي خوادم موجودة على المنفذ 8080...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
    echo إيقاف العملية %%a...
    taskkill /PID %%a /F >nul 2>&1
)

echo.
echo 🚀 تشغيل الخادم...
echo.
cd server
node working-server.js

echo.
echo 🛑 تم إيقاف الخادم
pause
