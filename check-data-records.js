const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "postgresql://postgres:yemen123@localhost:5432/yemclient_db"
    }
  }
});

async function checkAndCreateDataRecords() {
  try {
    console.log('🔍 فحص جدول data_records...');
    
    // فحص عدد السجلات الموجودة
    const count = await prisma.dataRecord.count();
    console.log(`📊 عدد السجلات الموجودة: ${count}`);
    
    if (count === 0) {
      console.log('📝 إنشاء بيانات تجريبية...');
      
      // الحصول على أول وكيل وعميل
      const agent = await prisma.agent.findFirst();
      const client = await prisma.client.findFirst();
      
      if (!agent || !client) {
        console.log('❌ لا يوجد وكلاء أو عملاء لإنشاء البيانات');
        return;
      }
      
      // إنشاء بيانات تجريبية
      const testData = [
        {
          agentId: agent.id,
          clientId: client.id,
          agentReference: 1001,
          operationStatus: 1,
          clientCode: client.clientCode.toString(),
          clientPassword: 'test123',
          clientIpAddress: '*************',
          operationDate: new Date('2024-01-15')
        },
        {
          agentId: agent.id,
          clientId: client.id,
          agentReference: 1002,
          operationStatus: 0,
          clientCode: client.clientCode.toString(),
          clientPassword: 'test123',
          clientIpAddress: '*************',
          operationDate: new Date('2024-01-16')
        },
        {
          agentId: agent.id,
          clientId: client.id,
          agentReference: 1003,
          operationStatus: 1,
          clientCode: client.clientCode.toString(),
          clientPassword: 'test123',
          clientIpAddress: '*************',
          operationDate: new Date('2024-01-17')
        }
      ];
      
      for (const data of testData) {
        await prisma.dataRecord.create({ data });
        console.log(`✅ تم إنشاء سجل: ${data.agentReference}`);
      }
      
      console.log('🎉 تم إنشاء البيانات التجريبية بنجاح!');
    }
    
    // عرض البيانات الموجودة
    const records = await prisma.dataRecord.findMany({
      include: {
        agent: { select: { agentName: true } },
        client: { select: { clientName: true } }
      },
      orderBy: { operationDate: 'desc' },
      take: 10
    });
    
    console.log('\n📋 البيانات الموجودة:');
    records.forEach(record => {
      console.log(`- ID: ${record.id}, وكيل: ${record.agent.agentName}, عميل: ${record.client.clientName}, حالة: ${record.operationStatus ? 'ناجح' : 'فاشل'}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAndCreateDataRecords();
