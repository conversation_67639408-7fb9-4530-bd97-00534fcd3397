// اختبار سريع لـ API المطورين
const http = require('http');

console.log('🧪 اختبار API المطورين...\n');

// بيانات الاختبار
const testData = {
  agent_login_name: 'testuser',
  agent_login_password: 'test123',
  client_code: 1004,
  client_token: 'TEST1004'
};

const postData = JSON.stringify(testData);

const options = {
  hostname: '***********',
  port: 8080,
  path: '/api/external/verify-direct',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  },
  timeout: 10000
};

console.log('📡 إرسال طلب إلى:', `http://${options.hostname}:${options.port}${options.path}`);
console.log('📋 البيانات المرسلة:', testData);
console.log('\n⏳ انتظار الاستجابة...\n');

const req = http.request(options, (res) => {
  console.log(`📊 رمز الاستجابة: ${res.statusCode}`);
  console.log(`📋 رؤوس الاستجابة:`, res.headers);
  
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('\n📄 محتوى الاستجابة:');
    console.log('========================');
    try {
      const jsonData = JSON.parse(data);
      console.log(JSON.stringify(jsonData, null, 2));
      
      if (jsonData.status === 'success') {
        console.log('\n✅ نجح الاختبار! API يعمل بشكل صحيح');
      } else {
        console.log('\n❌ فشل الاختبار:', jsonData.message || 'خطأ غير معروف');
      }
    } catch (error) {
      console.log('❌ خطأ في تحليل JSON:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ خطأ في الطلب:', error.message);
  
  if (error.code === 'ECONNREFUSED') {
    console.log('\n💡 نصائح لحل المشكلة:');
    console.log('   1. تأكد من تشغيل الخادم على المنفذ 8080');
    console.log('   2. شغل الأمر: node server/working-server.js');
    console.log('   3. أو انقر مرتين على ملف تشغيل-الخادم.bat');
  }
});

req.on('timeout', () => {
  console.log('⏰ انتهت مهلة الطلب');
  req.destroy();
});

req.write(postData);
req.end();
