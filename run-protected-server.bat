@echo off
chcp 65001 >nul
echo.
echo ========================================
echo     🔒 تشغيل الخادم المحمي - نظام إدارة العملاء اليمني
echo ========================================
echo.

echo 🛑 إيقاف أي خوادم موجودة على المنفذ 8080...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
    echo إيقاف العملية %%a...
    taskkill /PID %%a /F >nul 2>&1
)

echo.
echo 🔐 تشغيل الخادم المحمي مع JWT Authentication...
echo 📍 الخادم: http://localhost:8080
echo 🌐 خارجي: http://***********:8080
echo.
echo ⚠️  ملاحظة مهمة: جميع /api/* محمية الآن بـ JWT Token
echo ✅ APIs العامة فقط: /api/auth/login, /api/external/health
echo.

cd server
echo 🚀 بدء تشغيل working-server.js المحمي...
node working-server.js

echo.
echo 🛑 تم إيقاف الخادم
pause
