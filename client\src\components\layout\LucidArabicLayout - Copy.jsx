import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  Grid
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import UserProfileDialog from '../dialogs/UserProfileDialog';

const drawerWidth = 300;

// ألوان يمن موبايل
const YEMEN_MOBILE_COLORS = {
  primary: '#B40047',
  primaryLight: '#C0014E',
  secondary: '#FFFABD',
  dark: '#8A0036',
  light: '#FFFDF0',
  background: '#FFFDF0',
  text: '#4A1C31'
};

const LucidArabicLayout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [anchorEl, setAnchorEl] = useState(null);
  const [profileDialogOpen, setProfileDialogOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, hasPermission } = useAuth();

  useEffect(() => {
    console.log('🎨 تحميل تصميم يمن موبايل');
    document.body.style.direction = 'rtl';
    document.body.style.textAlign = 'right';
    document.documentElement.style.direction = 'rtl';
    document.body.style.backgroundColor = YEMEN_MOBILE_COLORS.background;

    return () => {
      document.body.style.backgroundColor = '';
    };
  }, []);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleProfileOpen = () => {
    setProfileDialogOpen(true);
    handleClose();
  };

  const handleLogout = () => {
    logout();
    handleClose();
  };

  // قوائم النظام مع أيقونات باستخدام ألوان يمن موبايل
  const systemMenus = [
    {
      text: 'لوحة التحكم',
      icon: '📊',
      path: '/dashboard',
      permission: null,
      gradient: `linear-gradient(135deg, ${YEMEN_MOBILE_COLORS.primary} 0%, ${YEMEN_MOBILE_COLORS.primaryLight} 100%)`,
      shadow: `0 8px 32px ${YEMEN_MOBILE_COLORS.primary}40`
    },
    {
      text: 'العملاء',
      icon: '👥',
      path: '/clients',
      permission: { resource: 'clients', action: 'read' },
      gradient: `linear-gradient(135deg, ${YEMEN_MOBILE_COLORS.primary} 0%, ${YEMEN_MOBILE_COLORS.dark} 100%)`,
      shadow: `0 8px 32px ${YEMEN_MOBILE_COLORS.primary}40`
    },
    {
      text: 'الوكلاء',
      icon: '🏢',
      path: '/agents',
      permission: { resource: 'agents', action: 'read' },
      gradient: `linear-gradient(135deg, ${YEMEN_MOBILE_COLORS.primaryLight} 0%, ${YEMEN_MOBILE_COLORS.primary} 100%)`,
      shadow: `0 8px 32px ${YEMEN_MOBILE_COLORS.primaryLight}40`
    },
    {
      text: 'المستخدمين',
      icon: '👤',
      path: '/users',
      permission: { resource: 'users', action: 'read' },
      gradient: `linear-gradient(135deg, ${YEMEN_MOBILE_COLORS.dark} 0%, ${YEMEN_MOBILE_COLORS.primary} 100%)`,
      shadow: `0 8px 32px ${YEMEN_MOBILE_COLORS.dark}40`
    },
    {
      text: 'البيانات',
      icon: '📋',
      path: '/data-records',
      permission: null,
      gradient: `linear-gradient(135deg, ${YEMEN_MOBILE_COLORS.primary} 0%, ${YEMEN_MOBILE_COLORS.primaryLight} 100%)`,
      shadow: `0 8px 32px ${YEMEN_MOBILE_COLORS.primary}40`
    },
    {
      text: 'الأمان',
      icon: '🔒',
      path: '/security',
      permission: { resource: 'security', action: 'read' },
      gradient: `linear-gradient(135deg, ${YEMEN_MOBILE_COLORS.primaryLight} 0%, ${YEMEN_MOBILE_COLORS.dark} 100%)`,
      shadow: `0 8px 32px ${YEMEN_MOBILE_COLORS.primaryLight}40`
    }
  ];

  const getPageTitle = () => {
    const currentItem = systemMenus.find(item => item.path === location.pathname);
    return currentItem?.text || 'لوحة التحكم';
  };

  // مكون القائمة الجانبية بألوان يمن موبايل
  const drawer = (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column', 
      direction: 'rtl',
      background: YEMEN_MOBILE_COLORS.background,
      boxShadow: '0 0 30px rgba(180, 0, 71, 0.1)',
      overflow: 'hidden'
    }}>
      {/* رأس القائمة - تصميم بألوان يمن موبايل */}
      <Box sx={{
        background: `linear-gradient(135deg, ${YEMEN_MOBILE_COLORS.primary} 0%, ${YEMEN_MOBILE_COLORS.dark} 100%)`,
        color: 'white',
        p: 3,
        pt: 10,
        position: 'relative',
        overflow: 'hidden',
        borderBottom: `1px solid ${YEMEN_MOBILE_COLORS.light}`,
        boxShadow: '0 5px 15px rgba(180, 0, 71, 0.3)',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 70%)',
          pointerEvents: 'none'
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: YEMEN_MOBILE_COLORS.secondary,
          backgroundSize: '200% 200%',
        }
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: 'column',
          textAlign: 'center',
          position: 'relative',
          zIndex: 1,
          mt: 4
        }}>
          <Avatar sx={{
            width: 90,
            height: 90,
            mb: 3,
            background: `linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%)`,
            fontSize: '36px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.4)',
            border: `2px solid ${YEMEN_MOBILE_COLORS.secondary}`,
            fontFamily: 'Khalid-Art-bold, sans-serif',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'scale(1.05) rotate(5deg)',
              boxShadow: '0 12px 40px rgba(0,0,0,0.5)',
              borderColor: YEMEN_MOBILE_COLORS.secondary
            }
          }}>
            👤
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="body2" sx={{
              opacity: 0.9,
              fontFamily: 'Khalid-Art-bold, sans-serif',
              fontSize: '18px',
              mb: 1,
              textShadow: '0 2px 4px rgba(0,0,0,0.3)',
              letterSpacing: '1px'
            }}>
              مرحباً،
            </Typography>
            <Typography variant="h6" sx={{
              fontWeight: 700,
              fontFamily: 'Khalid-Art-bold, sans-serif',
              fontSize: '24px',
              textShadow: '0 2px 4px rgba(0,0,0,0.3)',
              color: YEMEN_MOBILE_COLORS.secondary,
              letterSpacing: '0.5px'
            }}>
              {user?.username || 'مستخدم'}
            </Typography>
          </Box>
        </Box>

        {/* إحصائيات سريعة بتصميم يمن موبايل */}
        <Grid container spacing={2} sx={{ position: 'relative', zIndex: 1, mt: 3 }}>
          {[
            { value: '5+', label: 'سنوات خبرة' },
            { value: '400+', label: 'عميل' },
            { value: '80+', label: 'وكيل' }
          ].map((stat, index) => (
            <Grid item xs={4} key={index}>
              <Box sx={{
                textAlign: 'center',
                p: 1.5,
                borderRadius: 3,
                background: 'rgba(255,255,255,0.15)',
                backdropFilter: 'blur(10px)',
                border: `1px solid ${YEMEN_MOBILE_COLORS.secondary}`,
                boxShadow: '0 4px 20px rgba(0,0,0,0.2)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 8px 30px rgba(0,0,0,0.3)',
                  background: 'rgba(255,255,255,0.25)',
                }
              }}>
                <Typography variant="h6" sx={{
                  fontWeight: 700,
                  fontFamily: 'Khalid-Art-bold, sans-serif',
                  fontSize: '18px',
                  color: YEMEN_MOBILE_COLORS.secondary,
                  mb: 0.5
                }}>
                  {stat.value}
                </Typography>
                <Typography variant="caption" sx={{
                  opacity: 0.9,
                  fontFamily: 'Khalid-Art-bold, sans-serif',
                  fontSize: '12px',
                  letterSpacing: '0.5px',
                  color: 'white'
                }}>
                  {stat.label}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* قائمة التنقل بألوان يمن موبايل */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', pt: 2, px: 1 }}>
        <List sx={{ py: 0 }}>
          {systemMenus.map((item) => {
            if (item.permission && !hasPermission(item.permission.resource, item.permission.action)) {
              return null;
            }

            const isActive = location.pathname === item.path;

            return (
              <ListItem key={item.path} disablePadding sx={{ mb: 2 }}>
                <ListItemButton
                  onClick={() => navigate(item.path)}
                  sx={{
                    borderRadius: 3,
                    py: 2,
                    px: 2,
                    background: isActive ? item.gradient : 'rgba(180, 0, 71, 0.05)',
                    color: isActive ? 'white' : YEMEN_MOBILE_COLORS.text,
                    boxShadow: isActive ? item.shadow : '0 4px 20px rgba(180, 0, 71, 0.1)',
                    border: isActive ? 'none' : `1px solid ${YEMEN_MOBILE_COLORS.light}`,
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    direction: 'rtl',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: isActive ? 'none' : 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 100%)',
                      pointerEvents: 'none'
                    },
                    '&:hover': {
                      transform: 'translateY(-5px) scale(1.03)',
                      boxShadow: isActive ?
                        `0 12px 40px ${item.shadow.split('rgba(')[1].split(')')[0]}, 0.5)` :
                        `0 8px 32px ${YEMEN_MOBILE_COLORS.primary}30`,
                      background: isActive ? item.gradient : 'rgba(180, 0, 71, 0.1)',
                    },
                    '&:active': {
                      transform: 'translateY(-2px) scale(1.01)'
                    }
                  }}
                >
                  <ListItemIcon sx={{
                    minWidth: 50,
                    marginLeft: '16px',
                    marginRight: 0,
                    position: 'relative',
                    zIndex: 1
                  }}>
                    <Box sx={{
                      width: 45,
                      height: 45,
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: isActive ?
                        'rgba(255,255,255,0.2)' :
                        YEMEN_MOBILE_COLORS.secondary,
                      boxShadow: isActive ?
                        '0 4px 16px rgba(255,255,255,0.3)' :
                        '0 4px 16px rgba(0,0,0,0.1)',
                      transition: 'all 0.3s ease',
                      border: `1px solid ${isActive ? 'rgba(255,255,255,0.3)' : YEMEN_MOBILE_COLORS.light}`
                    }}>
                      <span style={{
                        fontSize: '22px',
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
                        transform: 'perspective(100px) rotateX(10deg)',
                        color: isActive ? 'white' : YEMEN_MOBILE_COLORS.primary
                      }}>
                        {item.icon}
                      </span>
                    </Box>
                  </ListItemIcon>
                  <ListItemText
                    primary={item.text}
                    sx={{
                      textAlign: 'right',
                      position: 'relative',
                      zIndex: 1,
                      '& .MuiListItemText-primary': {
                        fontWeight: 700,
                        fontSize: '17px',
                        textAlign: 'right',
                        fontFamily: 'Khalid-Art-bold, sans-serif',
                        letterSpacing: '0.5px',
                        color: isActive ? 'white' : YEMEN_MOBILE_COLORS.primary
                      }
                    }}
                  />
                </ListItemButton>
              </ListItem>
            );
          })}
        </List>
      </Box>
      
      {/* تذييل القائمة الجانبية */}
      <Box sx={{ 
        py: 2, 
        px: 3, 
        textAlign: 'center',
        background: YEMEN_MOBILE_COLORS.light,
        borderTop: `1px solid ${YEMEN_MOBILE_COLORS.light}`
      }}>
        <Typography sx={{ 
          color: YEMEN_MOBILE_COLORS.primary,
          fontSize: '12px',
          fontFamily: 'Khalid-Art-bold, sans-serif'
        }}>
          © 2023 يمن موبايل. جميع الحقوق محفوظة.
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      direction: 'rtl',
      backgroundColor: YEMEN_MOBILE_COLORS.background,
      position: 'relative',
      width: '100vw',
      overflow: 'hidden',
    }}>
      {/* شريط علوي بألوان يمن موبايل */}
      <AppBar
        position="fixed"
        sx={{
          width: '100vw',
          right: 0,
          left: 0,
          background: `linear-gradient(135deg, ${YEMEN_MOBILE_COLORS.primary} 0%, ${YEMEN_MOBILE_COLORS.dark} 100%)`,
          boxShadow: `0 4px 30px ${YEMEN_MOBILE_COLORS.primary}30`,
          zIndex: (theme) => theme.zIndex.drawer + 1,
          direction: 'rtl',
          borderBottom: `1px solid ${YEMEN_MOBILE_COLORS.secondary}`,
          height: '70px'
        }}
      >
        <Toolbar sx={{ 
          direction: 'rtl',
          minHeight: '70px !important'
        }}>
          <Tooltip title={sidebarOpen ? "إخفاء القائمة" : "إظهار القائمة"}>
            <IconButton
              color="inherit"
              aria-label="toggle sidebar"
              onClick={handleSidebarToggle}
              sx={{
                ml: 2,
                display: { xs: 'none', sm: 'flex' },
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.2)',
                }
              }}
            >
              <span style={{ fontSize: '20px', fontWeight: 'bold' }}>
                {sidebarOpen ? '◀' : '▶'}
              </span>
            </IconButton>
          </Tooltip>

          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ ml: 2, display: { sm: 'none' } }}
          >
            <span style={{ fontSize: '24px' }}>☰</span>
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{
            flexGrow: 1,
            fontWeight: 700,
            textAlign: 'right',
            fontFamily: 'Khalid-Art-bold, sans-serif',
            fontSize: '22px',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)',
            letterSpacing: '0.5px',
            color: YEMEN_MOBILE_COLORS.secondary,
            ml: 2
          }}>
            {getPageTitle()}
          </Typography>

          <Tooltip title="الإشعارات">
            <IconButton size="large" color="inherit" sx={{
              backgroundColor: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              width: '45px',
              height: '45px',
              mr: 1,
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'scale(1.1)',
                backgroundColor: 'rgba(255,255,255,0.2)'
              }
            }}>
              <Badge badgeContent={3} color="error">
                <span style={{ fontSize: '22px' }}>🔔</span>
              </Badge>
            </IconButton>
          </Tooltip>

          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenu}
            color="inherit"
            sx={{
              backgroundColor: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              width: '45px',
              height: '45px',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'scale(1.1)',
                backgroundColor: 'rgba(255,255,255,0.2)'
              }
            }}
          >
            <span style={{ fontSize: '22px' }}>👤</span>
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            sx={{ 
              direction: 'rtl',
              mt: 1,
              '& .MuiPaper-root': {
                background: YEMEN_MOBILE_COLORS.light,
                border: `1px solid ${YEMEN_MOBILE_COLORS.light}`,
                boxShadow: `0 10px 30px ${YEMEN_MOBILE_COLORS.primary}30`,
                borderRadius: '12px',
                minWidth: '200px',
                overflow: 'hidden'
              }
            }}
          >
            <MenuItem onClick={handleProfileOpen} sx={{ 
              direction: 'rtl', 
              textAlign: 'right',
              py: 1.5,
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(180, 0, 71, 0.05)'
              }
            }}>
              <ListItemIcon sx={{ minWidth: '40px !important' }}>
                <span style={{ fontSize: '20px', color: YEMEN_MOBILE_COLORS.primary }}>👤</span>
              </ListItemIcon>
              <Typography sx={{ 
                fontFamily: 'Khalid-Art-bold, sans-serif',
                color: YEMEN_MOBILE_COLORS.primary,
                fontSize: '16px'
              }}>
                الملف الشخصي
              </Typography>
            </MenuItem>
            <MenuItem onClick={handleLogout} sx={{ 
              direction: 'rtl', 
              textAlign: 'right',
              py: 1.5,
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(180, 0, 71, 0.05)'
              }
            }}>
              <ListItemIcon sx={{ minWidth: '40px !important' }}>
                <span style={{ fontSize: '20px', color: YEMEN_MOBILE_COLORS.primary }}>🚪</span>
              </ListItemIcon>
              <Typography sx={{ 
                fontFamily: 'Khalid-Art-bold, sans-serif',
                color: YEMEN_MOBILE_COLORS.primary,
                fontSize: '16px'
              }}>
                تسجيل الخروج
              </Typography>
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* القائمة الجانبية على اليمين */}
      <Drawer
        variant="permanent"
        sx={{
          width: sidebarOpen ? drawerWidth : 0,
          flexShrink: 0,
          transition: 'width 0.3s ease-in-out',
          '& .MuiDrawer-paper': {
            width: sidebarOpen ? drawerWidth : 0,
            boxSizing: 'border-box',
            position: 'fixed',
            right: 0,
            left: 'auto',
            top: 0,
            height: '100vh',
            direction: 'rtl',
            zIndex: 1100,
            borderLeft: sidebarOpen ? `1px solid ${YEMEN_MOBILE_COLORS.light}` : 'none',
            borderRight: 'none',
            overflow: 'hidden',
            transition: 'width 0.3s ease-in-out'
          },
          display: { xs: 'none', sm: 'block' }
        }}
      >
        {sidebarOpen && drawer}
      </Drawer>

      {/* القائمة المؤقتة للموبايل */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            direction: 'rtl',
            background: YEMEN_MOBILE_COLORS.background,
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* المحتوى الرئيسي - يتوسع عند إخفاء القائمة */}
      <Box
        component="main"
        sx={{
          position: 'fixed',
          top: '70px',
          right: sidebarOpen ? `${drawerWidth}px` : '0px',
          left: 0,
          bottom: 0,
          backgroundColor: YEMEN_MOBILE_COLORS.background,
          direction: 'rtl',
          overflow: 'auto',
          width: sidebarOpen ? `calc(100vw - ${drawerWidth}px)` : '100vw',
          height: 'calc(100vh - 70px)',
          transition: 'right 0.3s ease-in-out, width 0.3s ease-in-out',
        }}
      >
        <Box sx={{
          p: 3,
          direction: 'rtl',
          width: '100%',
          height: '100%',
          minHeight: 'calc(100vh - 70px - 32px)',
        }}>
          {children}
        </Box>
      </Box>

      {/* نافذة الملف الشخصي */}
      <UserProfileDialog
        open={profileDialogOpen}
        onClose={() => setProfileDialogOpen(false)}
      />
    </Box>
  );
};

export default LucidArabicLayout;