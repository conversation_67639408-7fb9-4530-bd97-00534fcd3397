// تشغيل الخادم الأصلي
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting working server...');

// Kill any existing processes on port 8080
const { exec } = require('child_process');
exec('netstat -ano | findstr :8080', (error, stdout) => {
  if (stdout) {
    console.log('🛑 Found processes on port 8080, killing them...');
    const lines = stdout.split('\n');
    lines.forEach(line => {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 5) {
        const pid = parts[4];
        if (pid && pid !== '0') {
          exec(`taskkill /PID ${pid} /F`, () => {});
        }
      }
    });
  }
  
  // Start server after a delay
  setTimeout(() => {
    const serverPath = path.join(__dirname, 'server', 'working-server.js');
    console.log('Server path:', serverPath);

    const server = spawn('node', [serverPath], {
      stdio: 'inherit',
      cwd: __dirname
    });

    server.on('error', (error) => {
      console.error('❌ Server error:', error);
    });

    server.on('close', (code) => {
      console.log(`🛑 Server exited with code: ${code}`);
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down...');
      server.kill();
      process.exit(0);
    });
  }, 2000);
});
