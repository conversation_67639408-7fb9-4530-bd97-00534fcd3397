const https = require('https');
const http = require('http');

console.log('🔒 اختبار حماية API على العنوان الخارجي');
console.log('===============================================');
console.log();

// إعدادات الاختبار
const EXTERNAL_HOST = '***********';
const PORT = 8080;
const BASE_URL = `http://${EXTERNAL_HOST}:${PORT}`;

// بيانات تسجيل الدخول للاختبار
const LOGIN_DATA = {
  loginName: 'admin',
  password: 'admin123456',
  deviceId: 'security_test_device_external'
};

let testResults = {
  unprotectedAccess: null,
  loginSuccess: null,
  protectedAccess: null,
  invalidToken: null
};

// دالة لإرسال طلب HTTP
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: jsonData,
            headers: res.headers
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// اختبار 1: محاولة الوصول بدون Token
async function testUnprotectedAccess() {
  console.log('🧪 اختبار 1: محاولة الوصول لـ /api/users بدون Token');
  console.log(`📡 URL: ${BASE_URL}/api/users`);
  
  try {
    const options = {
      hostname: EXTERNAL_HOST,
      port: PORT,
      path: '/api/users',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 401) {
      console.log('✅ ممتاز! API محمي - تم رفض الوصول بدون Token');
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Message: ${response.data.message || 'Unauthorized'}`);
      testResults.unprotectedAccess = true;
    } else if (response.statusCode === 200) {
      console.log('❌ خطر أمني! API غير محمي - تم السماح بالوصول بدون Token');
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Data exposed: ${JSON.stringify(response.data).substring(0, 200)}...`);
      testResults.unprotectedAccess = false;
    } else {
      console.log(`⚠️ استجابة غير متوقعة: ${response.statusCode}`);
      console.log(`   Data: ${JSON.stringify(response.data)}`);
      testResults.unprotectedAccess = null;
    }
  } catch (error) {
    console.log(`❌ خطأ في الاتصال: ${error.message}`);
    testResults.unprotectedAccess = null;
  }
  
  console.log();
}

// اختبار 2: تسجيل الدخول للحصول على Token
async function testLogin() {
  console.log('🧪 اختبار 2: تسجيل الدخول للحصول على JWT Token');
  console.log(`📡 URL: ${BASE_URL}/api/auth/login`);
  
  try {
    const options = {
      hostname: EXTERNAL_HOST,
      port: PORT,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const response = await makeRequest(options, LOGIN_DATA);
    
    if (response.statusCode === 200 && response.data.success && response.data.token) {
      console.log('✅ تم تسجيل الدخول بنجاح');
      console.log(`   User: ${response.data.user.username}`);
      console.log(`   Token Type: ${response.data.tokenType}`);
      console.log(`   Token: ${response.data.token.substring(0, 50)}...`);
      testResults.loginSuccess = true;
      return response.data.token;
    } else {
      console.log('❌ فشل تسجيل الدخول');
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Error: ${response.data.error || response.data.message || 'Unknown error'}`);
      testResults.loginSuccess = false;
      return null;
    }
  } catch (error) {
    console.log(`❌ خطأ في الاتصال: ${error.message}`);
    testResults.loginSuccess = null;
    return null;
  }
  
  console.log();
}

// اختبار 3: الوصول مع Token صحيح
async function testProtectedAccess(token) {
  console.log('🧪 اختبار 3: الوصول لـ /api/users مع JWT Token صحيح');
  console.log(`📡 URL: ${BASE_URL}/api/users`);
  
  if (!token) {
    console.log('❌ لا يوجد Token للاختبار');
    testResults.protectedAccess = null;
    return;
  }
  
  try {
    const options = {
      hostname: EXTERNAL_HOST,
      port: PORT,
      path: '/api/users',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200) {
      console.log('✅ تم الوصول بنجاح مع Token صحيح');
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Users count: ${response.data.data?.length || 0}`);
      console.log(`   Total: ${response.data.total || 0}`);
      testResults.protectedAccess = true;
    } else {
      console.log('❌ فشل الوصول مع Token صحيح');
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Error: ${response.data.message || 'Unknown error'}`);
      testResults.protectedAccess = false;
    }
  } catch (error) {
    console.log(`❌ خطأ في الاتصال: ${error.message}`);
    testResults.protectedAccess = null;
  }
  
  console.log();
}

// اختبار 4: الوصول مع Token خاطئ
async function testInvalidToken() {
  console.log('🧪 اختبار 4: الوصول لـ /api/users مع JWT Token خاطئ');
  console.log(`📡 URL: ${BASE_URL}/api/users`);
  
  try {
    const options = {
      hostname: EXTERNAL_HOST,
      port: PORT,
      path: '/api/users',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid_fake_token_123456789'
      }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 401) {
      console.log('✅ ممتاز! تم رفض Token خاطئ');
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Message: ${response.data.message || 'Invalid token'}`);
      testResults.invalidToken = true;
    } else if (response.statusCode === 200) {
      console.log('❌ خطر أمني! تم قبول Token خاطئ');
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Data: ${JSON.stringify(response.data).substring(0, 200)}...`);
      testResults.invalidToken = false;
    } else {
      console.log(`⚠️ استجابة غير متوقعة: ${response.statusCode}`);
      console.log(`   Data: ${JSON.stringify(response.data)}`);
      testResults.invalidToken = null;
    }
  } catch (error) {
    console.log(`❌ خطأ في الاتصال: ${error.message}`);
    testResults.invalidToken = null;
  }
  
  console.log();
}

// عرض ملخص النتائج
function showSummary() {
  console.log('📊 ملخص نتائج اختبار الحماية');
  console.log('===============================================');
  console.log();
  
  // اختبار الوصول بدون Token
  if (testResults.unprotectedAccess === true) {
    console.log('✅ الوصول بدون Token: محمي بشكل صحيح');
  } else if (testResults.unprotectedAccess === false) {
    console.log('❌ الوصول بدون Token: غير محمي (خطر أمني!)');
  } else {
    console.log('⚠️ الوصول بدون Token: لم يتم الاختبار');
  }
  
  // اختبار تسجيل الدخول
  if (testResults.loginSuccess === true) {
    console.log('✅ تسجيل الدخول: يعمل بشكل صحيح');
  } else if (testResults.loginSuccess === false) {
    console.log('❌ تسجيل الدخول: فشل');
  } else {
    console.log('⚠️ تسجيل الدخول: لم يتم الاختبار');
  }
  
  // اختبار الوصول المحمي
  if (testResults.protectedAccess === true) {
    console.log('✅ الوصول مع Token صحيح: يعمل بشكل صحيح');
  } else if (testResults.protectedAccess === false) {
    console.log('❌ الوصول مع Token صحيح: فشل');
  } else {
    console.log('⚠️ الوصول مع Token صحيح: لم يتم الاختبار');
  }
  
  // اختبار Token خاطئ
  if (testResults.invalidToken === true) {
    console.log('✅ رفض Token خاطئ: محمي بشكل صحيح');
  } else if (testResults.invalidToken === false) {
    console.log('❌ رفض Token خاطئ: غير محمي (خطر أمني!)');
  } else {
    console.log('⚠️ رفض Token خاطئ: لم يتم الاختبار');
  }
  
  console.log();
  
  // تقييم عام
  const passedTests = Object.values(testResults).filter(r => r === true).length;
  const failedTests = Object.values(testResults).filter(r => r === false).length;
  const totalTests = Object.values(testResults).filter(r => r !== null).length;
  
  console.log(`📈 النتيجة العامة: ${passedTests}/${totalTests} اختبار نجح`);
  console.log();
  
  if (failedTests === 0 && totalTests > 0) {
    console.log('🎉 ممتاز! النظام محمي بشكل كامل على العنوان الخارجي');
    console.log(`✅ ${BASE_URL} آمن ومحمي`);
  } else if (failedTests > 0) {
    console.log('⚠️ يوجد مشاكل أمنية تحتاج إصلاح!');
    console.log(`❌ ${BASE_URL} غير آمن`);
  } else {
    console.log('💡 لم يتم إجراء اختبارات كافية');
  }
  
  console.log();
  console.log('===============================================');
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  console.log(`🎯 بدء اختبار الحماية على: ${BASE_URL}`);
  console.log();
  
  // اختبار 1: الوصول بدون Token
  await testUnprotectedAccess();
  
  // اختبار 2: تسجيل الدخول
  const token = await testLogin();
  
  // اختبار 3: الوصول مع Token صحيح
  await testProtectedAccess(token);
  
  // اختبار 4: الوصول مع Token خاطئ
  await testInvalidToken();
  
  // عرض الملخص
  showSummary();
}

// تشغيل الاختبارات
runAllTests().catch(error => {
  console.error('❌ خطأ في تشغيل الاختبارات:', error.message);
  process.exit(1);
});
