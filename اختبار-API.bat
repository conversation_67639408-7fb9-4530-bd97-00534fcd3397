@echo off
chcp 65001 >nul
title اختبار API - YemClient
color 0E

echo.
echo ========================================
echo            اختبار API للمطورين
echo ========================================
echo.

echo 🔍 فحص الخادم على المنفذ 8080...
netstat -an | find "8080" >nul 2>&1
if %errorlevel%==0 (
    echo ✅ الخادم يعمل على المنفذ 8080
) else (
    echo ❌ الخادم لا يعمل على المنفذ 8080
    echo 💡 قم بتشغيل: تشغيل-مباشر.bat أولاً
    echo.
    echo اضغط أي مفتاح للإغلاق...
    pause >nul
    exit /b 1
)

echo.
echo 🧪 اختبار API...
echo ========================================
echo.

echo 📡 إرسال طلب إلى: http://localhost:8080/api/external/verify-direct
echo 📋 البيانات: testuser / test123 / 1000 / ABC12345
echo.

powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8080/api/external/verify-direct' -Method Post -ContentType 'application/json' -Body '{\"agent_login_name\":\"testuser\",\"agent_login_password\":\"test123\",\"client_code\":\"1000\",\"client_token\":\"ABC12345\"}' -TimeoutSec 10; Write-Host '✅ نجح الاتصال! النتيجة:' -ForegroundColor Green; Write-Host '========================================' -ForegroundColor Yellow; $response | ConvertTo-Json -Depth 3; Write-Host '========================================' -ForegroundColor Yellow } catch { Write-Host '❌ فشل الاتصال:' -ForegroundColor Red; Write-Host $_.Exception.Message -ForegroundColor Red; if ($_.Exception.Message -like '*JSON*') { Write-Host '🔧 الخادم يرسل HTML بدلاً من JSON - تحقق من الخادم' -ForegroundColor Yellow } }"

echo.
echo ========================================
echo                 النتيجة
echo ========================================
echo.
echo 📋 التفسير:
echo   ✅ إذا ظهر JSON أعلاه = الخادم يعمل بشكل صحيح
echo   ❌ إذا ظهر خطأ JSON = الخادم يرسل HTML بدلاً من JSON
echo   ❌ إذا ظهر خطأ اتصال = الخادم لا يعمل
echo.
echo 🎯 إذا نجح الاختبار، يمكنك الآن استخدام صفحة المطورين!
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
