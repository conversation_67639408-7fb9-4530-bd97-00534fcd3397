package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gorilla/mux"
	"github.com/joho/godotenv"
	"github.com/swaggo/http-swagger"
	"gorm.io/gorm"

	"yemclient-server/internal/database"
	"yemclient-server/internal/handlers"
	"yemclient-server/internal/logger"
	_ "yemclient-server/docs" // Swagger docs
)

// @title Yemen Client API
// @version 1.0
// @description API documentation for Yemen Client Management System
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8081
// @BasePath /
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization

var (
	db *gorm.DB
)

func main() {
	// Load .env file
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	// Initialize logger
	logger.Init()
	log.Println("Logger initialized")

	// Connect to database
	db, err = database.Connect()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	database.DB = db
	log.Println(" Database connected successfully")

	// Setup router with logging middleware
	r := mux.NewRouter()
	r.Use(loggingMiddleware)

	// Swagger documentation
	r.PathPrefix("/swagger/").Handler(httpSwagger.Handler(
		httpSwagger.URL("/swagger/doc.json"),
	))

	// Auth routes
	r.HandleFunc("/api/auth/login", handlers.LoginHandler).Methods("POST")
	r.HandleFunc("/api/auth/logout", handlers.LogoutHandler).Methods("POST")

	// Protected routes
	apiRouter := r.PathPrefix("/api").Subrouter()
	apiRouter.Use(handlers.AuthMiddleware)

	// Users routes
	apiRouter.HandleFunc("/api/users", handlers.GetUsersHandler).Methods("GET")

	// Clients routes
	apiRouter.HandleFunc("/api/clients", handlers.GetClientsHandler).Methods("GET")
	apiRouter.HandleFunc("/api/clients", handlers.CreateClientHandler).Methods("POST")

	// Agents routes
	apiRouter.HandleFunc("/api/agents", handlers.GetAgentsHandler).Methods("GET")

	// Data records routes
	apiRouter.HandleFunc("/api/data-records", handlers.GetDataRecordsHandler).Methods("GET")

	// Security routes
	apiRouter.HandleFunc("/api/security/stats", handlers.GetSecurityStatsHandler).Methods("GET")
	apiRouter.HandleFunc("/api/security/login-attempts", handlers.GetLoginAttemptsHandler).Methods("GET")

	// External API routes
	apiRouter.HandleFunc("/api/external/health", handlers.ExternalHealthHandler).Methods("GET")
	apiRouter.HandleFunc("/api/external/stats", handlers.ExternalStatsHandler).Methods("GET")

	// Health check
	r.HandleFunc("/health", handlers.HealthHandler).Methods("GET")

	// Register routes
	handlers.RegisterRoutes(r)

	// Update server port configuration
	port := "8083"

	// Server configuration
	srv := &http.Server{
		Addr:         ":" + port,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 30 * time.Second,
	}

	// Graceful shutdown setup
	done := make(chan os.Signal, 1)
	signal.Notify(done, os.Interrupt, syscall.SIGINT, syscall.SIGTERM)

	// Start server
	go func() {
		log.Printf(" Server running on port %s\n", port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf(" Server error: %v\n", err)
		}
	}()

	// Wait for shutdown signal
	<-done
	log.Println("\n Server is shutting down...")

	// Shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Printf(" Server shutdown error: %v\n", err)
	}

	// Close database connection
	if sqlDB, err := db.DB(); err == nil {
		sqlDB.Close()
	}

	log.Println(" Server exited properly")
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		logger.Info("Request:", 
			"method", r.Method,
			"path", r.URL.Path,
			"ip", r.RemoteAddr)
		next.ServeHTTP(w, r)
	})
}
