import React, { useState } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Tooltip,
  Snackbar,
  Alert
} from '@mui/material'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'

const DeviceIdDisplay = ({ deviceId, label = "رقم الجهاز:", readOnly = false }) => {
  const [copySuccess, setCopySuccess] = useState(false)

  // إزالة كلمة "device_" من بداية الرقم
  const formatDeviceId = (id) => {
    if (!id) return ''
    return id.startsWith('device_') ? id.substring(7) : id
  }

  // نسخ رقم الجهاز إلى الحافظة
  const handleCopy = async () => {
    try {
      const formattedId = formatDeviceId(deviceId)
      await navigator.clipboard.writeText(formattedId)
      setCopySuccess(true)
    } catch (err) {
      console.error('فشل في نسخ رقم الجهاز:', err)
      // fallback للمتصفحات القديمة
      const textArea = document.createElement('textarea')
      textArea.value = formatDeviceId(deviceId)
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        setCopySuccess(true)
      } catch (fallbackErr) {
        console.error('فشل في النسخ باستخدام fallback:', fallbackErr)
      }
      document.body.removeChild(textArea)
    }
  }

  const handleCloseSnackbar = () => {
    setCopySuccess(false)
  }

  if (!deviceId) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography variant="body2" color="text.secondary">
          {label}
        </Typography>
        <Typography variant="body2" color="text.disabled">
          غير محدد
        </Typography>
      </Box>
    )
  }

  const formattedId = formatDeviceId(deviceId)

  return (
    <>
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 1,
        p: 1,
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 1,
        backgroundColor: 'background.paper'
      }}>
        <Typography variant="body2" fontWeight="medium" color="text.primary">
          {label}
        </Typography>
        <Typography 
          variant="body2" 
          sx={{ 
            fontFamily: 'monospace',
            backgroundColor: 'grey.100',
            px: 1,
            py: 0.5,
            borderRadius: 0.5,
            border: '1px solid',
            borderColor: 'grey.300',
            flex: 1
          }}
        >
          {formattedId}
        </Typography>
        {!readOnly && (
          <Tooltip title="نسخ الكود" arrow>
            <IconButton
              size="small"
              onClick={handleCopy}
              sx={{
                color: 'primary.main',
                backgroundColor: 'primary.light',
                '&:hover': { 
                  backgroundColor: 'primary.main', 
                  color: 'white' 
                },
                borderRadius: '6px',
                padding: '6px'
              }}
            >
              <ContentCopyIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      <Snackbar
        open={copySuccess}
        autoHideDuration={2000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity="success" 
          sx={{ width: '100%' }}
        >
          تم نسخ رقم الجهاز بنجاح!
        </Alert>
      </Snackbar>
    </>
  )
}

export default DeviceIdDisplay
