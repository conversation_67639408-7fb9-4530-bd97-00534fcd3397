@echo off
chcp 65001 >nul
title إعداد وتشغيل خدمة Remote Agent

color 0A
echo ========================================
echo     إعداد خدمة Remote Agent
echo ========================================

echo.
echo 🔍 فحص متطلبات النظام...

:: التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متاح
node --version

:: التحقق من PostgreSQL
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ PostgreSQL غير متاح في PATH
    echo تأكد من تثبيت PostgreSQL وإضافته للـ PATH
) else (
    echo ✅ PostgreSQL متاح
    psql --version
)

echo.
echo 📦 تثبيت متطلبات المشروع...

:: تثبيت متطلبات الجذر
echo تثبيت متطلبات المشروع الرئيسي...
npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت متطلبات المشروع الرئيسي
    pause
    exit /b 1
)

:: تثبيت متطلبات الخادم
echo تثبيت متطلبات الخادم...
cd server
npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت متطلبات الخادم
    pause
    exit /b 1
)

:: تثبيت متطلبات العميل
echo تثبيت متطلبات العميل...
cd ..\client
npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت متطلبات العميل
    pause
    exit /b 1
)

cd ..

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!

echo.
echo 🗄️ إعداد قاعدة البيانات...

:: إنشاء قاعدة البيانات
echo إنشاء قاعدة البيانات...
psql -U postgres -c "CREATE DATABASE yemclient_db;" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ قاعدة البيانات موجودة مسبقاً أو فشل في الإنشاء
)

:: تشغيل سكريبت إنشاء الجداول
echo إنشاء الجداول...
psql -U postgres -d yemclient_db -f create_database.sql
if %errorlevel% neq 0 (
    echo ⚠️ فشل في إنشاء الجداول - تأكد من كلمة مرور postgres
)

echo.
echo 🚀 تشغيل الخدمات...

:: تشغيل الخادم
echo تشغيل خادم API...
cd server
start "YemClient API Server" cmd /k "npm start"

:: انتظار تشغيل الخادم
echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak >nul

:: تشغيل العميل
echo تشغيل واجهة العميل...
cd ..\client
start "YemClient Frontend" cmd /k "npm run dev"

cd ..

echo.
echo ========================================
echo       النظام يعمل الآن! 🎉
echo ========================================

echo.
echo 🌐 الوصول للنظام:
echo ├─ واجهة العميل: http://localhost:5173
echo ├─ API الخادم: http://localhost:8081
echo ├─ API الخارجي: http://***********:8081
echo └─ API الوكلاء: http://***********:8081/api/external/

echo.
echo 🔑 بيانات الدخول الافتراضية:
echo ├─ Username: admin
echo └─ Password: admin123456

echo.
echo 📊 معلومات قاعدة البيانات:
echo ├─ Host: localhost
echo ├─ Port: 5432
echo ├─ Database: yemclient_db
echo ├─ Username: postgres
echo └─ Password: yemen123

echo.
echo 🧪 اختبار API الوكلاء:
echo curl -X POST http://***********:8081/api/external/verify-direct ^
echo   -H "Content-Type: application/json" ^
echo   -d "{\"username\": \"testuser\", \"password\": \"test123\", \"clientCode\": 1004, \"clientToken\": \"TEST1004\"}"

echo.
echo ✨ النظام جاهز للاستخدام!
echo.
pause
