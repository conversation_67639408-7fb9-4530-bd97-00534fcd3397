// إصلاح اتصال قاعدة البيانات وإنشاء المستخدم hash8080
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

// إعداد Prisma مع رابط قاعدة البيانات الصحيح
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "postgresql://postgres:yemen123@localhost:5432/yemclient_db"
    }
  }
});

async function fixDatabaseConnection() {
  console.log('🔧 إصلاح اتصال قاعدة البيانات وإنشاء المستخدم hash8080');
  console.log('=' .repeat(60));
  
  try {
    // 1. اختبار الاتصال بقاعدة البيانات
    console.log('🔌 اختبار الاتصال بقاعدة البيانات...');
    await prisma.$connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // 2. التحقق من وجود جدول المستخدمين
    console.log('📋 التحقق من جدول المستخدمين...');
    const userCount = await prisma.user.count();
    console.log(`✅ جدول المستخدمين موجود - عدد المستخدمين: ${userCount}`);
    
    // 3. البحث عن المستخدم hash8080
    console.log('🔍 البحث عن المستخدم hash8080...');
    let user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    if (user) {
      console.log('✅ المستخدم hash8080 موجود:');
      console.log(`   ID: ${user.id}`);
      console.log(`   الاسم: ${user.username}`);
      console.log(`   اسم الدخول: ${user.loginName}`);
      console.log(`   نشط: ${user.isActive}`);
      console.log(`   تاريخ الإنشاء: ${user.createdAt.toLocaleString('ar-EG')}`);
      
      // اختبار كلمة المرور الحالية
      console.log('\n🧪 اختبار كلمة المرور الحالية...');
      const fullUser = await prisma.user.findUnique({
        where: { loginName: 'hash8080' }
      });
      
      const passwords = ['Hash8080', 'hash8080', 'HASH8080'];
      let correctPassword = null;
      
      for (const testPassword of passwords) {
        const isMatch = await bcrypt.compare(testPassword, fullUser.password);
        console.log(`   ${testPassword}: ${isMatch ? '✅ صحيحة' : '❌ خطأ'}`);
        if (isMatch) correctPassword = testPassword;
      }
      
      if (correctPassword) {
        console.log(`\n🎯 كلمة المرور الصحيحة: ${correctPassword}`);
      } else {
        console.log('\n⚠️ لم يتم العثور على كلمة المرور الصحيحة - سيتم إعادة تعيينها');
        
        // إعادة تعيين كلمة المرور إلى hash8080
        const newPassword = 'hash8080';
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        
        await prisma.user.update({
          where: { loginName: 'hash8080' },
          data: { 
            password: hashedPassword,
            updatedAt: new Date()
          }
        });
        
        console.log(`✅ تم إعادة تعيين كلمة المرور إلى: ${newPassword}`);
        correctPassword = newPassword;
      }
      
    } else {
      console.log('❌ المستخدم hash8080 غير موجود - سيتم إنشاؤه');
      
      // إنشاء المستخدم hash8080
      const password = 'hash8080';
      const hashedPassword = await bcrypt.hash(password, 10);
      
      user = await prisma.user.create({
        data: {
          username: 'محمد الحاشدي',
          loginName: 'hash8080',
          password: hashedPassword,
          permissions: {
            isAdmin: true,
            clients: { create: true, read: true, update: true, delete: true },
            agents: { create: true, read: true, update: true, delete: true },
            users: { create: true, read: true, update: true, delete: true },
            dashboard: { read: true },
            security: { read: true, manage: true }
          },
          isActive: true,
          deviceId: 'default-device',
          device1: null
        },
        select: {
          id: true,
          username: true,
          loginName: true,
          isActive: true,
          createdAt: true
        }
      });
      
      console.log('✅ تم إنشاء المستخدم hash8080 بنجاح:');
      console.log(`   ID: ${user.id}`);
      console.log(`   الاسم: ${user.username}`);
      console.log(`   اسم الدخول: ${user.loginName}`);
      console.log(`   كلمة المرور: ${password}`);
    }
    
    // 4. اختبار تسجيل الدخول
    console.log('\n🔐 اختبار تسجيل الدخول...');
    const loginTest = await prisma.user.findFirst({
      where: {
        OR: [
          { loginName: 'hash8080' },
          { username: 'hash8080' }
        ],
        isActive: true
      }
    });
    
    if (loginTest) {
      console.log('✅ يمكن العثور على المستخدم عند تسجيل الدخول');
      
      // اختبار كلمة المرور
      const testPassword = 'hash8080';
      const isPasswordValid = await bcrypt.compare(testPassword, loginTest.password);
      
      if (isPasswordValid) {
        console.log(`✅ كلمة المرور ${testPassword} صحيحة`);
      } else {
        console.log(`❌ كلمة المرور ${testPassword} خاطئة`);
      }
    } else {
      console.log('❌ لا يمكن العثور على المستخدم عند تسجيل الدخول');
    }
    
    // 5. عرض الملخص النهائي
    console.log('\n📋 الملخص النهائي:');
    console.log('=' .repeat(40));
    console.log('✅ قاعدة البيانات متصلة');
    console.log('✅ جدول المستخدمين موجود');
    console.log('✅ المستخدم hash8080 موجود ونشط');
    console.log('\n🎯 بيانات تسجيل الدخول:');
    console.log('   اسم المستخدم: hash8080');
    console.log('   كلمة المرور: hash8080');
    console.log('   الرابط: http://127.0.0.1:8080/');
    
    console.log('\n💡 تأكد من:');
    console.log('   1. تشغيل الخادم: node server/working-server.js');
    console.log('   2. فتح الرابط: http://127.0.0.1:8080/');
    console.log('   3. استخدام البيانات أعلاه لتسجيل الدخول');
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح قاعدة البيانات:', error);
    
    if (error.code === 'P1001') {
      console.error('\n💡 لا يمكن الاتصال بقاعدة البيانات:');
      console.error('   - تأكد من تشغيل PostgreSQL');
      console.error('   - تحقق من إعدادات الاتصال في .env');
      console.error('   - تأكد من وجود قاعدة البيانات yemclient_db');
    } else if (error.code === 'P2021') {
      console.error('\n💡 جدول المستخدمين غير موجود:');
      console.error('   - قم بتشغيل: npx prisma migrate dev');
      console.error('   - أو: npx prisma db push');
    } else {
      console.error('\n💡 تفاصيل الخطأ:', error.message);
    }
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 تم إغلاق الاتصال بقاعدة البيانات');
  }
}

console.log('🚀 بدء إصلاح اتصال قاعدة البيانات...');
fixDatabaseConnection()
  .then(() => {
    console.log('\n🏁 انتهى الإصلاح بنجاح');
    console.log('يمكنك الآن تسجيل الدخول بالبيانات المعروضة أعلاه');
  })
  .catch((error) => {
    console.error('\n💥 فشل الإصلاح:', error.message);
    console.log('راجع الأخطاء أعلاه وحاول مرة أخرى');
  });
