// إنشاء الوكيل التجريبي للمطورين
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createTestAgent() {
  try {
    console.log('🧪 إنشاء الوكيل التجريبي للمطورين...');

    // التحقق من وجود الوكيل
    const existingAgent = await prisma.agent.findFirst({
      where: { loginName: 'testuser' }
    });

    if (existingAgent) {
      console.log('✅ الوكيل التجريبي موجود بالفعل:', {
        id: existingAgent.id,
        loginName: existingAgent.loginName,
        agentName: existingAgent.agentName,
        isActive: existingAgent.isActive
      });
      return;
    }

    // إنشاء الوكيل التجريبي
    const hashedPassword = await bcrypt.hash('test123', 10);
    
    const testAgent = await prisma.agent.create({
      data: {
        agentName: 'وكيل تجريبي للمطورين',
        agencyName: 'وكالة تجريبية',
        agencyType: 'وكالة اختبار',
        ipAddress: '*************',
        loginName: 'testuser',
        loginPassword: hashedPassword,
        isActive: true
      }
    });

    console.log('✅ تم إنشاء الوكيل التجريبي بنجاح:', {
      id: testAgent.id,
      loginName: testAgent.loginName,
      agentName: testAgent.agentName,
      isActive: testAgent.isActive
    });

    console.log('\n🔐 بيانات الدخول للمطورين:');
    console.log('اسم المستخدم: testuser');
    console.log('كلمة المرور: test123');

  } catch (error) {
    console.error('❌ خطأ في إنشاء الوكيل التجريبي:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestAgent();
