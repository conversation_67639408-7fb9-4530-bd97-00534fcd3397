const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 8080;

console.log('🚀 Starting Test Server...');

// Middleware
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Test login endpoint
app.post('/api/auth/login', (req, res) => {
  const { loginName, password } = req.body;
  console.log('🔐 Test Login:', { loginName, passwordLength: password?.length });

  // Simple test response
  if (loginName === 'admin' && password === 'admin123456') {
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: 1,
        username: 'admin',
        loginName: 'admin',
        permissions: 'all',
        isActive: true
      },
      token: `token_1_${Date.now()}`
    });
  } else {
    res.status(401).json({ success: false, error: 'بيانات الدخول غير صحيحة' });
  }
});

// Simple HTML page for testing
app.get('/login', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - اختبار</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-container {
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                width: 100%;
                max-width: 400px;
            }
            .title {
                text-align: center;
                color: #333;
                margin-bottom: 30px;
                font-size: 24px;
            }
            .form-group {
                margin-bottom: 20px;
            }
            label {
                display: block;
                margin-bottom: 5px;
                color: #555;
                font-weight: bold;
            }
            input {
                width: 100%;
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
                box-sizing: border-box;
            }
            input:focus {
                border-color: #667eea;
                outline: none;
            }
            button {
                width: 100%;
                padding: 12px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 16px;
                cursor: pointer;
                font-weight: bold;
            }
            button:hover {
                opacity: 0.9;
            }
            .message {
                margin-top: 15px;
                padding: 10px;
                border-radius: 5px;
                text-align: center;
            }
            .success {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .error {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <h1 class="title">🔐 تسجيل الدخول</h1>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginName">اسم المستخدم:</label>
                    <input type="text" id="loginName" name="loginName" value="admin" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" value="admin123456" required>
                </div>
                <button type="submit">دخول</button>
            </form>
            <div id="message"></div>
        </div>

        <script>
            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const loginName = document.getElementById('loginName').value;
                const password = document.getElementById('password').value;
                const messageDiv = document.getElementById('message');

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ loginName, password, deviceId: 'test-device' })
                    });

                    const data = await response.json();

                    if (data.success) {
                        messageDiv.innerHTML = '<div class="success">✅ ' + data.message + '</div>';
                        setTimeout(() => {
                            window.location.href = '/dashboard';
                        }, 1500);
                    } else {
                        messageDiv.innerHTML = '<div class="error">❌ ' + data.error + '</div>';
                    }
                } catch (error) {
                    messageDiv.innerHTML = '<div class="error">❌ خطأ في الاتصال بالخادم</div>';
                }
            });
        </script>
    </body>
    </html>
  `);
});

// Dashboard page
app.get('/dashboard', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background: #f5f5f5;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                text-align: center;
            }
            .cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }
            .card {
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .card h3 {
                margin-top: 0;
                color: #333;
            }
            .status {
                color: #28a745;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎛️ لوحة التحكم - نظام إدارة العملاء اليمني</h1>
            <p>مرحباً بك في النظام</p>
        </div>

        <div class="cards">
            <div class="card">
                <h3>📊 حالة الخادم</h3>
                <p>الخادم: <span class="status">يعمل بشكل طبيعي</span></p>
                <p>المنفذ: 8080</p>
                <p>الوقت: ${new Date().toLocaleString('ar-EG')}</p>
            </div>

            <div class="card">
                <h3>🔐 المصادقة</h3>
                <p>نظام تسجيل الدخول: <span class="status">نشط</span></p>
                <p>المستخدم الحالي: admin</p>
                <p>الصلاحيات: جميع الصلاحيات</p>
            </div>

            <div class="card">
                <h3>🗄️ قاعدة البيانات</h3>
                <p>الحالة: <span style="color: #ffc107;">قيد الاختبار</span></p>
                <p>النوع: PostgreSQL</p>
                <p>الاتصال: محلي</p>
            </div>

            <div class="card">
                <h3>📋 الصفحات المتاحة</h3>
                <p>• صفحة تسجيل الدخول: /login</p>
                <p>• لوحة التحكم: /dashboard</p>
                <p>• فحص الصحة: /health</p>
            </div>
        </div>

        <div class="card">
            <h3>📝 ملاحظات</h3>
            <p>هذا خادم اختبار بسيط للتحقق من عمل النظام الأساسي.</p>
            <p>لتشغيل النظام الكامل، يجب:</p>
            <ul>
                <li>تشغيل قاعدة البيانات PostgreSQL</li>
                <li>تشغيل الخادم الرئيسي working-server.js</li>
                <li>بناء الواجهة الأمامية React</li>
            </ul>
        </div>
    </body>
    </html>
  `);
});

// Root redirect
app.get('/', (req, res) => {
  res.redirect('/login');
});

// Start server
app.listen(PORT, () => {
  console.log(`✅ Test Server running on http://localhost:${PORT}`);
  console.log(`📁 Serving static files from: ${path.join(__dirname, '../client/build')}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down test server...');
  process.exit(0);
});
