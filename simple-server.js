const express = require('express');
const path = require('path');

const app = express();
const PORT = 8080;

// تقديم الملفات الثابتة من مجلد client/dist
app.use(express.static(path.join(__dirname, 'client', 'dist')));

// تقديم index.html لجميع المسارات
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client', 'dist', 'index.html'));
});

app.listen(PORT, () => {
    console.log(`🚀 الخادم يعمل على http://localhost:${PORT}`);
    console.log(`📁 يقدم الملفات من: ${path.join(__dirname, 'client', 'dist')}`);
});
