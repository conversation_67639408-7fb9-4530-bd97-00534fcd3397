import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Paper
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Search,
  Visibility,
  FilterList,
  Person,
  Group,
  Business,
  Dashboard,
  Security,
  Settings,
  Home,
  AccountCircle,
  ExitToApp
} from '@mui/icons-material';

const TestIcons = () => {
  const icons = [
    { name: 'Add', component: Add, label: 'إضافة' },
    { name: 'Edit', component: Edit, label: 'تعديل' },
    { name: 'Delete', component: Delete, label: 'حذف' },
    { name: 'Search', component: Search, label: 'بحث' },
    { name: 'Visibility', component: Visibility, label: 'عرض' },
    { name: 'FilterList', component: FilterList, label: 'فلترة' },
    { name: 'Person', component: Person, label: 'شخص' },
    { name: 'Group', component: Group, label: 'مجموعة' },
    { name: 'Business', component: Business, label: 'أعمال' },
    { name: 'Dashboard', component: Dashboard, label: 'لوحة التحكم' },
    { name: 'Security', component: Security, label: 'أمان' },
    { name: 'Setting<PERSON>', component: Settings, label: 'إعدادات' },
    { name: 'Home', component: Home, label: 'الرئيسية' },
    { name: 'AccountCircle', component: AccountCircle, label: 'حساب' },
    { name: 'ExitToApp', component: ExitToApp, label: 'خروج' }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, textAlign: 'center' }}>
        🔍 اختبار الأيقونات
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            ✅ اختبار الأزرار مع الأيقونات:
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Button
                variant="contained"
                startIcon={<Add />}
                fullWidth
                sx={{ mb: 1 }}
              >
                إضافة عميل جديد
              </Button>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Button
                variant="contained"
                startIcon={<Person />}
                fullWidth
                sx={{ mb: 1 }}
                color="secondary"
              >
                إضافة مستخدم جديد
              </Button>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Button
                variant="contained"
                startIcon={<Business />}
                fullWidth
                sx={{ mb: 1 }}
                color="success"
              >
                إضافة وكيل جديد
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            🎨 جميع الأيقونات المتاحة:
          </Typography>
          
          <Grid container spacing={2}>
            {icons.map((icon) => {
              const IconComponent = icon.component;
              return (
                <Grid item xs={6} md={3} lg={2} key={icon.name}>
                  <Paper
                    sx={{
                      p: 2,
                      textAlign: 'center',
                      height: 120,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      '&:hover': {
                        backgroundColor: 'action.hover'
                      }
                    }}
                  >
                    <IconComponent sx={{ fontSize: 40, mb: 1, color: 'primary.main' }} />
                    <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                      {icon.label}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {icon.name}
                    </Typography>
                  </Paper>
                </Grid>
              );
            })}
          </Grid>
        </CardContent>
      </Card>

      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            🔧 معلومات التشخيص:
          </Typography>
          
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>المتصفح:</strong> {navigator.userAgent}
          </Typography>
          
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>الوقت:</strong> {new Date().toLocaleString('ar-SA')}
          </Typography>
          
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>الرابط:</strong> {window.location.href}
          </Typography>

          <Box sx={{ mt: 2 }}>
            <Button
              variant="outlined"
              onClick={() => window.location.reload()}
              startIcon={<Settings />}
            >
              إعادة تحميل الصفحة
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default TestIcons;
