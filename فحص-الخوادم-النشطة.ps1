# فحص الخوادم النشطة وإيقافها
Write-Host "🔍 فحص الخوادم النشطة..." -ForegroundColor Yellow

# فحص العمليات النشطة
Write-Host "`n📋 العمليات النشطة:" -ForegroundColor Cyan
Get-Process -Name "node" -ErrorAction SilentlyContinue | Format-Table Id, ProcessName, StartTime, Path -AutoSize

# فحص المنافذ النشطة
Write-Host "`n🌐 المنافذ النشطة:" -ForegroundColor Cyan
Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue | Format-Table LocalAddress, LocalPort, State, OwningProcess -AutoSize

# إيقاف جميع عمليات Node.js
Write-Host "`n🛑 إيقاف جميع عمليات Node.js..." -ForegroundColor Red
try {
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "✅ تم إيقاف عمليات Node.js" -ForegroundColor Green
} catch {
    Write-Host "ℹ️ لم توجد عمليات Node.js للإيقاف" -ForegroundColor Yellow
}

# إيقاف العمليات على المنفذ 8080
Write-Host "`n🛑 إيقاف العمليات على المنفذ 8080..." -ForegroundColor Red
try {
    $connections = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
    foreach ($conn in $connections) {
        Stop-Process -Id $conn.OwningProcess -Force -ErrorAction SilentlyContinue
        Write-Host "✅ تم إيقاف العملية $($conn.OwningProcess)" -ForegroundColor Green
    }
} catch {
    Write-Host "ℹ️ لم توجد عمليات على المنفذ 8080" -ForegroundColor Yellow
}

# انتظار قليل
Start-Sleep -Seconds 3

# فحص نهائي
Write-Host "`n🔍 فحص نهائي للمنافذ..." -ForegroundColor Yellow
$stillRunning = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
if ($stillRunning) {
    Write-Host "⚠️ لا تزال هناك عمليات تعمل على المنفذ 8080:" -ForegroundColor Red
    $stillRunning | Format-Table LocalAddress, LocalPort, State, OwningProcess -AutoSize
} else {
    Write-Host "✅ تم إيقاف جميع العمليات على المنفذ 8080" -ForegroundColor Green
}

Write-Host "`n🚀 الآن يمكن تشغيل الخادم المحمي..." -ForegroundColor Green
Write-Host "تشغيل الأمر: cd server && node working-server.js" -ForegroundColor White

Read-Host "`nاضغط Enter للمتابعة"
