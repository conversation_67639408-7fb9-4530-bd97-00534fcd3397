import { createContext, useContext, useState, useEffect } from 'react';

// Context للتنقل الداخلي
const StaticRouterContext = createContext();

/**
 * Router مخصص يحافظ على URL ثابت
 */
export function StaticUrlRouter({ children, initialRoute = '/' }) {
  const [currentRoute, setCurrentRoute] = useState(initialRoute);
  const [history, setHistory] = useState([initialRoute]);

  // منع تغيير URL في المتصفح
  useEffect(() => {
    const handlePopState = (event) => {
      event.preventDefault();
      // إعادة URL للحالة الأصلية
      window.history.replaceState(null, '', window.location.pathname);
    };

    const handleBeforeUnload = () => {
      // التأكد من أن URL يبقى ثابت
      window.history.replaceState(null, '', window.location.pathname);
    };

    // منع تغيير URL
    window.addEventListener('popstate', handlePopState);
    window.addEventListener('beforeunload', handleBeforeUnload);

    // إعادة تعيين URL عند التحميل
    const currentUrl = window.location.pathname;
    window.history.replaceState(null, '', currentUrl);

    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const navigate = (route, replace = false) => {
    if (replace) {
      setHistory(prev => [...prev.slice(0, -1), route]);
    } else {
      setHistory(prev => [...prev, route]);
    }
    setCurrentRoute(route);
    
    // منع تغيير URL في المتصفح
    window.history.replaceState(null, '', window.location.pathname);
  };

  const goBack = () => {
    if (history.length > 1) {
      const newHistory = history.slice(0, -1);
      setHistory(newHistory);
      setCurrentRoute(newHistory[newHistory.length - 1]);
    }
  };

  const contextValue = {
    currentRoute,
    navigate,
    goBack,
    history
  };

  return (
    <StaticRouterContext.Provider value={contextValue}>
      {children}
    </StaticRouterContext.Provider>
  );
}

/**
 * Hook للوصول لوظائف التنقل
 */
export function useStaticRouter() {
  const context = useContext(StaticRouterContext);
  if (!context) {
    throw new Error('useStaticRouter must be used within StaticUrlRouter');
  }
  return context;
}

/**
 * مكون Route مخصص
 */
export function StaticRoute({ path, element, exact = false }) {
  const { currentRoute } = useStaticRouter();
  
  const isMatch = exact 
    ? currentRoute === path 
    : currentRoute.startsWith(path);
    
  return isMatch ? element : null;
}

/**
 * مكون Routes مخصص
 */
export function StaticRoutes({ children }) {
  const { currentRoute } = useStaticRouter();
  
  // البحث عن Route المطابق
  const matchedRoute = children.find(child => {
    if (!child.props) return false;
    
    const { path, exact = false } = child.props;
    
    if (exact) {
      return currentRoute === path;
    } else {
      return currentRoute.startsWith(path) || currentRoute === path;
    }
  });

  return matchedRoute || null;
}

/**
 * مكون Link مخصص
 */
export function StaticLink({ to, children, className, style, onClick }) {
  const { navigate } = useStaticRouter();

  const handleClick = (e) => {
    e.preventDefault();
    navigate(to);
    if (onClick) onClick(e);
  };

  return (
    <a 
      href="#" 
      onClick={handleClick}
      className={className}
      style={style}
    >
      {children}
    </a>
  );
}

/**
 * مكون Navigate مخصص
 */
export function StaticNavigate({ to, replace = false }) {
  const { navigate } = useStaticRouter();

  useEffect(() => {
    navigate(to, replace);
  }, [to, replace, navigate]);

  return null;
}

/**
 * Hook للتحقق من المسار الحالي
 */
export function useStaticLocation() {
  const { currentRoute } = useStaticRouter();
  
  return {
    pathname: currentRoute,
    search: '',
    hash: '',
    state: null
  };
}

/**
 * مكون لحماية المسارات
 */
export function ProtectedStaticRoute({ element, condition, fallback }) {
  return condition ? element : fallback;
}
