// اختبار API مباشر
const http = require('http');

console.log('🔍 اختبار API المطورين مباشر');
console.log('============================');

// اختبار المنفذ 8080 أولاً
function testPort8080() {
  return new Promise((resolve) => {
    console.log('\n🔍 اختبار المنفذ 8080...');
    
    const req = http.request({
      hostname: '***********',
      port: 8080,
      path: '/api/external/verify-direct',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log(`📊 Status Code: ${res.statusCode}`);
        console.log('📄 Response:', data.substring(0, 500));
        resolve(res.statusCode === 200 || res.statusCode === 400);
      });
    });

    req.on('error', (err) => {
      console.log('❌ خطأ في الاتصال 8080:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال 8080');
      req.destroy();
      resolve(false);
    });

    // إرسال بيانات اختبار
    const testData = JSON.stringify({
      agent_login_name: 'testuser',
      agent_login_password: 'test123',
      client_code: 1004,
      client_token: 'TEST1004'
    });

    req.write(testData);
    req.end();
  });
}

// اختبار المنفذ 8081
function testPort8081() {
  return new Promise((resolve) => {
    console.log('\n🔍 اختبار المنفذ 8081...');
    
    const req = http.request({
      hostname: '***********',
      port: 8081,
      path: '/api/external/verify-direct',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log(`📊 Status Code: ${res.statusCode}`);
        console.log('📄 Response:', data.substring(0, 500));
        resolve(res.statusCode === 200 || res.statusCode === 400);
      });
    });

    req.on('error', (err) => {
      console.log('❌ خطأ في الاتصال 8081:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال 8081');
      req.destroy();
      resolve(false);
    });

    // إرسال بيانات اختبار
    const testData = JSON.stringify({
      username: 'testuser',
      password: 'test123',
      clientCode: 1004,
      clientToken: 'TEST1004',
      deviceId: 'DEV001'
    });

    req.write(testData);
    req.end();
  });
}

// اختبار Health Check
function testHealthCheck() {
  return new Promise((resolve) => {
    console.log('\n🔍 اختبار Health Check على 8080...');
    
    const req = http.request({
      hostname: '***********',
      port: 8080,
      path: '/api/external/health',
      method: 'GET',
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log(`📊 Status Code: ${res.statusCode}`);
        console.log('📄 Response:', data.substring(0, 300));
        resolve(res.statusCode === 200);
      });
    });

    req.on('error', (err) => {
      console.log('❌ خطأ في Health Check:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة Health Check');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  console.log('🚀 بدء الاختبارات الشاملة...\n');
  
  const healthWorks = await testHealthCheck();
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const port8080Works = await testPort8080();
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const port8081Works = await testPort8081();
  
  console.log('\n📋 النتائج النهائية:');
  console.log('=================');
  console.log('🌐 Health Check (8080):', healthWorks ? '✅ يعمل' : '❌ لا يعمل');
  console.log('🔗 API المطورين (8080):', port8080Works ? '✅ يعمل' : '❌ لا يعمل');
  console.log('🔗 API المطورين (8081):', port8081Works ? '✅ يعمل' : '❌ لا يعمل');
  
  if (healthWorks && port8080Works) {
    console.log('\n🎉 الخادم الأصلي (8080) يعمل بشكل مثالي!');
    console.log('🔗 استخدم: http://***********:8080/api/external/verify-direct');
  } else if (port8081Works) {
    console.log('\n🎉 الخادم الآمن (8081) يعمل!');
    console.log('🔗 استخدم: http://***********:8081/api/external/verify-direct');
  } else {
    console.log('\n❌ لا يوجد خادم يعمل حالياً');
  }
}

runAllTests().catch(console.error);
