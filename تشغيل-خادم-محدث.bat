@echo off
chcp 65001 >nul
echo.
echo ========================================
echo         تشغيل خادم Yemen Client
echo ========================================
echo.

echo 🔍 فحص المنفذ 8080...
netstat -an | find "8080" >nul
if %errorlevel%==0 (
    echo ⚠️ المنفذ 8080 مستخدم بالفعل!
    echo.
    echo 🛑 إيقاف العمليات على المنفذ 8080...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
        echo    إيقاف العملية %%a...
        taskkill /PID %%a /F >nul 2>&1
    )
    echo ✅ تم إيقاف العمليات
    timeout /t 2 /nobreak >nul
)

echo.
echo 🚀 تشغيل الخادم...
echo    الملف: server/working-server.js
echo    المنفذ: 8080
echo    الرابط المحلي: http://localhost:8080
echo    الرابط الخارجي: http://***********:8080
echo    API المطورين: /api/external/verify-direct
echo.

cd server
echo 🎯 بدء تشغيل الخادم...
node working-server.js

pause
