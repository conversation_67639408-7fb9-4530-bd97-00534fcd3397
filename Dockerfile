# استخدام Node.js 18 Alpine
FROM node:18-alpine

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات package
COPY package*.json ./

# تثبيت المتطلبات
RUN npm ci

# نسخ باقي الملفات
COPY . .

# إنشاء مستخدم غير root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# تغيير ملكية الملفات
RUN chown -R nodejs:nodejs /app
USER nodejs

# كشف المنفذ
EXPOSE 5173

# أمر البداية
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
