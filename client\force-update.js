// إجبار تحديث الملفات في dist
const fs = require('fs');
const path = require('path');

console.log('🔄 إجبار تحديث الملفات...');

// إنشاء ملف JavaScript جديد يحتوي على التعديلات
const updatedJS = `
// تحديث إجباري للنموذج
window.FORCE_UPDATE_TIMESTAMP = ${Date.now()};

// إضافة CSS للحقول الجديدة
const style = document.createElement('style');
style.textContent = \`
  .device-field-updated {
    border-left: 3px solid #4caf50 !important;
  }
  .device-field-updated .MuiInputLabel-root {
    color: #4caf50 !important;
  }
\`;
document.head.appendChild(style);

// إضافة معالج للنموذج
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تم تحديث النموذج - الطابع الزمني:', window.FORCE_UPDATE_TIMESTAMP);
    
    // البحث عن حقول الأجهزة وتحديثها
    setTimeout(() => {
        const deviceFields = document.querySelectorAll('input[placeholder*="device"]');
        deviceFields.forEach(field => {
            field.classList.add('device-field-updated');
            console.log('✅ تم تحديث حقل الجهاز:', field.placeholder);
        });
        
        // إضافة تنبيه للمستخدم
        if (deviceFields.length > 0) {
            const notification = document.createElement('div');
            notification.style.cssText = \`
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4caf50;
                color: white;
                padding: 15px;
                border-radius: 5px;
                z-index: 9999;
                font-family: Arial, sans-serif;
                direction: rtl;
            \`;
            notification.textContent = 'تم تحديث حقول معرف الجهاز بنجاح!';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    }, 1000);
});
`;

// كتابة الملف
const forceUpdatePath = path.join(__dirname, 'dist', 'force-update.js');
fs.writeFileSync(forceUpdatePath, updatedJS);

console.log('✅ تم إنشاء ملف التحديث الإجباري');

// تحديث index.html لتضمين الملف الجديد
const indexPath = path.join(__dirname, 'dist', 'index.html');
if (fs.existsSync(indexPath)) {
    let indexContent = fs.readFileSync(indexPath, 'utf8');
    
    // إضافة script tag قبل إغلاق body
    if (!indexContent.includes('force-update.js')) {
        indexContent = indexContent.replace(
            '</body>',
            '  <script src="/force-update.js"></script>\n</body>'
        );
        
        fs.writeFileSync(indexPath, indexContent);
        console.log('✅ تم تحديث index.html');
    }
}

console.log('🎉 تم التحديث بنجاح! افتح المتصفح وانتقل إلى صفحة المستخدمين');
