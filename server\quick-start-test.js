// Quick Test Server - أبسط خادم ممكن للاختبار
const http = require('http');
const url = require('url');

const PORT = 8080;

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  
  console.log(`${new Date().toISOString()} - ${req.method} ${path}`);
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  if (path === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'OK', timestamp: new Date().toISOString() }));
    return;
  }
  
  if (path === '/login' || path === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>اختبار الخادم - تسجيل الدخول</title>
          <style>
              body {
                  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  margin: 0;
                  padding: 20px;
                  min-height: 100vh;
                  display: flex;
                  align-items: center;
                  justify-content: center;
              }
              .container {
                  background: white;
                  padding: 40px;
                  border-radius: 15px;
                  box-shadow: 0 15px 35px rgba(0,0,0,0.2);
                  width: 100%;
                  max-width: 400px;
                  text-align: center;
              }
              .title {
                  color: #333;
                  margin-bottom: 30px;
                  font-size: 28px;
                  font-weight: bold;
              }
              .success {
                  background: #d4edda;
                  color: #155724;
                  padding: 20px;
                  border-radius: 10px;
                  border: 2px solid #c3e6cb;
                  margin-bottom: 20px;
              }
              .info {
                  background: #d1ecf1;
                  color: #0c5460;
                  padding: 15px;
                  border-radius: 8px;
                  border: 1px solid #bee5eb;
                  margin-bottom: 15px;
                  text-align: right;
              }
              .button {
                  display: inline-block;
                  padding: 12px 24px;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white;
                  text-decoration: none;
                  border-radius: 8px;
                  font-weight: bold;
                  margin: 10px;
                  transition: opacity 0.3s;
              }
              .button:hover {
                  opacity: 0.9;
              }
          </style>
      </head>
      <body>
          <div class="container">
              <h1 class="title">🎉 الخادم يعمل بنجاح!</h1>
              
              <div class="success">
                  ✅ تم تشغيل الخادم التجريبي بنجاح على المنفذ ${PORT}
              </div>
              
              <div class="info">
                  <strong>📋 معلومات الخادم:</strong><br>
                  • المنفذ: ${PORT}<br>
                  • الوقت: ${new Date().toLocaleString('ar-EG')}<br>
                  • الحالة: نشط ويعمل<br>
                  • النوع: خادم اختبار مبسط
              </div>
              
              <div class="info">
                  <strong>🔗 الروابط المتاحة:</strong><br>
                  • الصفحة الرئيسية: <a href="/">http://localhost:${PORT}/</a><br>
                  • فحص الصحة: <a href="/health">http://localhost:${PORT}/health</a><br>
                  • لوحة التحكم: <a href="/dashboard">http://localhost:${PORT}/dashboard</a>
              </div>
              
              <a href="/dashboard" class="button">🎛️ لوحة التحكم</a>
              <a href="/health" class="button">🔍 فحص الصحة</a>
          </div>
      </body>
      </html>
    `);
    return;
  }
  
  if (path === '/dashboard') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>لوحة التحكم - اختبار</title>
          <style>
              body {
                  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                  margin: 0;
                  padding: 20px;
                  background: #f8f9fa;
              }
              .header {
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white;
                  padding: 30px;
                  border-radius: 15px;
                  margin-bottom: 30px;
                  text-align: center;
                  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
              }
              .cards {
                  display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                  gap: 20px;
              }
              .card {
                  background: white;
                  padding: 25px;
                  border-radius: 12px;
                  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
                  border: 1px solid #e9ecef;
              }
              .card h3 {
                  margin-top: 0;
                  color: #495057;
                  font-size: 20px;
              }
              .status-good { color: #28a745; font-weight: bold; }
              .status-warning { color: #ffc107; font-weight: bold; }
              .status-info { color: #17a2b8; font-weight: bold; }
          </style>
      </head>
      <body>
          <div class="header">
              <h1>🎛️ لوحة التحكم - نظام إدارة العملاء اليمني</h1>
              <p>خادم الاختبار يعمل بنجاح</p>
          </div>
          
          <div class="cards">
              <div class="card">
                  <h3>🖥️ حالة الخادم</h3>
                  <p>الحالة: <span class="status-good">يعمل بشكل طبيعي</span></p>
                  <p>المنفذ: ${PORT}</p>
                  <p>بدء التشغيل: ${new Date().toLocaleString('ar-EG')}</p>
                  <p>النوع: HTTP Server</p>
              </div>
              
              <div class="card">
                  <h3>📊 الإحصائيات</h3>
                  <p>الطلبات المعالجة: متغيرة</p>
                  <p>وقت الاستجابة: <span class="status-good">سريع</span></p>
                  <p>استخدام الذاكرة: <span class="status-info">منخفض</span></p>
                  <p>حالة الشبكة: <span class="status-good">متصل</span></p>
              </div>
              
              <div class="card">
                  <h3>🗄️ قاعدة البيانات</h3>
                  <p>الحالة: <span class="status-warning">غير متصلة (اختبار)</span></p>
                  <p>النوع: PostgreSQL</p>
                  <p>الاتصال: محلي</p>
                  <p>المنفذ: 5432</p>
              </div>
              
              <div class="card">
                  <h3>🔧 الخطوات التالية</h3>
                  <p>لتشغيل النظام الكامل:</p>
                  <ul style="text-align: right;">
                      <li>تشغيل قاعدة البيانات PostgreSQL</li>
                      <li>تشغيل working-server.js</li>
                      <li>بناء الواجهة الأمامية React</li>
                      <li>اختبار جميع الوظائف</li>
                  </ul>
              </div>
          </div>
      </body>
      </html>
    `);
    return;
  }
  
  // 404 for other paths
  res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
  res.end(`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>404 - الصفحة غير موجودة</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                text-align: center;
                padding: 50px;
                background: #f8f9fa;
            }
            .error {
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                display: inline-block;
            }
        </style>
    </head>
    <body>
        <div class="error">
            <h1>404 - الصفحة غير موجودة</h1>
            <p>الصفحة المطلوبة: ${path}</p>
            <a href="/">العودة للصفحة الرئيسية</a>
        </div>
    </body>
    </html>
  `);
});

server.listen(PORT, () => {
  console.log(`🚀 Quick Test Server running on http://localhost:${PORT}`);
  console.log(`📅 Started at: ${new Date().toLocaleString('ar-EG')}`);
  console.log(`🔗 Open: http://localhost:${PORT}/login`);
});

server.on('error', (err) => {
  console.error('❌ Server error:', err);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
