import React, { useState, useEffect } from 'react'
import {
  Box,
  Drawer,
  AppBar,
  <PERSON><PERSON><PERSON>,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip
} from '@mui/material'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

const drawerWidth = 280

const SimpleArabicLayout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout, hasPermission } = useAuth()

  // إجبار التحديث والأنماط
  useEffect(() => {
    console.log('🎨 تحميل التخطيط العربي المبسط')

    // إضافة أنماط CSS مباشرة للجسم
    document.body.style.direction = 'rtl'
    document.body.style.textAlign = 'right'
    document.documentElement.style.direction = 'rtl'

    // إضافة class للجسم
    document.body.classList.add('arabic-layout')

    return () => {
      document.body.classList.remove('arabic-layout')
    }
  }, [])

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleLogout = () => {
    logout()
    handleClose()
  }

  const menuItems = [
    {
      text: 'لوحة التحكم',
      icon: '📊',
      path: '/dashboard',
      permission: null
    },
    {
      text: 'العملاء',
      icon: '👥',
      path: '/clients',
      permission: { resource: 'clients', action: 'read' }
    },
    {
      text: 'الوكلاء',
      icon: '🏢',
      path: '/agents',
      permission: { resource: 'agents', action: 'read' }
    },
    {
      text: 'المستخدمين',
      icon: '👤',
      path: '/users',
      permission: { resource: 'users', action: 'read' }
    },
    {
      text: 'البيانات',
      icon: '📋',
      path: '/data-records',
      permission: null
    },
    {
      text: 'الأمان',
      icon: '🔒',
      path: '/security',
      permission: { resource: 'security', action: 'read' }
    }
  ]

  const getPageTitle = () => {
    const currentItem = menuItems.find(item => item.path === location.pathname)
    return currentItem?.text || 'لوحة التحكم'
  }

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* رأس القائمة */}
      <Box sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        p: 3,
        textAlign: 'center'
      }}>
        <Typography variant="h6" sx={{ fontWeight: 700, mb: 2 }}>
          نظام إدارة العملاء
        </Typography>
        <Avatar sx={{
          width: 64,
          height: 64,
          mx: 'auto',
          mb: 1,
          bgcolor: 'rgba(255,255,255,0.2)',
          fontSize: '24px'
        }}>
          👤
        </Avatar>
        <Typography variant="body2" sx={{ opacity: 0.9 }}>
          {user?.username || 'مستخدم'}
        </Typography>
      </Box>

      <Divider />

      {/* قائمة التنقل */}
      <List sx={{ flexGrow: 1, py: 1 }}>
        {menuItems.map((item) => {
          if (item.permission && !hasPermission(item.permission.resource, item.permission.action)) {
            return null
          }

          const isActive = location.pathname === item.path

          return (
            <ListItem key={item.path} disablePadding sx={{ px: 1 }}>
              <ListItemButton
                onClick={() => navigate(item.path)}
                sx={{
                  borderRadius: 2,
                  mx: 1,
                  mb: 0.5,
                  backgroundColor: isActive ? 'primary.main' : 'transparent',
                  color: isActive ? 'white' : 'text.primary',
                  '&:hover': {
                    backgroundColor: isActive ? 'primary.dark' : 'action.hover',
                  },
                  transition: 'all 0.2s ease-in-out',
                  direction: 'rtl',
                  textAlign: 'right'
                }}
              >
                <ListItemIcon sx={{
                  minWidth: 40,
                  color: isActive ? 'white' : 'text.secondary',
                  marginLeft: '12px',
                  marginRight: 0
                }}>
                  <span style={{ fontSize: '20px' }}>{item.icon}</span>
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  sx={{
                    textAlign: 'right',
                    '& .MuiListItemText-primary': {
                      fontWeight: isActive ? 600 : 400,
                      textAlign: 'right'
                    }
                  }}
                />
              </ListItemButton>
            </ListItem>
          )
        })}
      </List>
    </Box>
  )

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      direction: 'rtl'
    }}>
      {/* شريط علوي */}
      <AppBar
        position="fixed"
        sx={{
          width: '100%',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          direction: 'rtl'
        }}
      >
        <Toolbar sx={{ direction: 'rtl' }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ ml: 2, display: { sm: 'none' } }}
          >
            <span style={{ fontSize: '24px' }}>☰</span>
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 600, textAlign: 'right' }}>
            {getPageTitle()}
          </Typography>

          <Tooltip title="الإشعارات">
            <IconButton size="large" color="inherit">
              <Badge badgeContent={0} color="error">
                <span style={{ fontSize: '24px' }}>🔔</span>
              </Badge>
            </IconButton>
          </Tooltip>

          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenu}
            color="inherit"
          >
            <span style={{ fontSize: '24px' }}>👤</span>
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            sx={{ direction: 'rtl' }}
          >
            <MenuItem onClick={handleClose} sx={{ direction: 'rtl', textAlign: 'right' }}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>👤</span>
              </ListItemIcon>
              الملف الشخصي
            </MenuItem>
            <MenuItem onClick={handleLogout} sx={{ direction: 'rtl', textAlign: 'right' }}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>🚪</span>
              </ListItemIcon>
              تسجيل الخروج
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* القائمة الجانبية على اليمين - بقوة */}
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
            position: 'fixed',
            right: 0,
            left: 'auto',
            top: 0,
            height: '100vh',
            direction: 'rtl',
            zIndex: 1100
          },
          display: { xs: 'none', sm: 'block' }
        }}
      >
        {drawer}
      </Drawer>

      {/* القائمة المؤقتة للموبايل */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            direction: 'rtl'
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        className="main-content-area"
        sx={{
          flexGrow: 1,
          marginRight: { sm: `${drawerWidth}px` },
          marginLeft: 0,
          minHeight: '100vh',
          backgroundColor: '#f8f9fa',
          direction: 'rtl',
          paddingTop: '64px',
          width: { sm: `calc(100% - ${drawerWidth}px)` }
        }}
      >
        <Box sx={{
          p: 2,
          direction: 'rtl',
          width: '100%',
          maxWidth: '100%'
        }}>
          {children}
        </Box>
      </Box>
    </Box>
  )
}

export default SimpleArabicLayout
