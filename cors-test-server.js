// خادم اختبار مع CORS صحيح
const http = require('http');
const url = require('url');

const PORT = 3000; // استخدام منفذ مختلف لتجنب التعارض

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;
  
  console.log(`${new Date().toISOString()} - ${method} ${path}`);
  
  // إعداد CORS headers لجميع الطلبات
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
  
  // التعامل مع preflight requests
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Health check
  if (path === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      status: 'ok', 
      message: 'CORS Test Server is running',
      timestamp: new Date().toISOString(),
      port: PORT
    }));
    return;
  }
  
  // Login API
  if (path === '/api/auth/login' && method === 'POST') {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('🔐 Login attempt:', { loginName: data.loginName, hasPassword: !!data.password });
        
        // اختبار بسيط لتسجيل الدخول
        if (data.loginName === 'admin' && data.password === 'admin123456') {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'تم تسجيل الدخول بنجاح',
            user: {
              id: 1,
              username: 'admin',
              loginName: 'admin',
              permissions: 'all',
              isActive: true
            },
            token: `test_token_${Date.now()}`
          }));
        } else {
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            error: 'بيانات الدخول غير صحيحة'
          }));
        }
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: 'خطأ في تحليل البيانات'
        }));
      }
    });
    return;
  }
  
  // الصفحة الرئيسية
  if (path === '/' || path === '/login') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>خادم اختبار CORS</title>
          <style>
              body {
                  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  margin: 0;
                  padding: 20px;
                  min-height: 100vh;
                  display: flex;
                  align-items: center;
                  justify-content: center;
              }
              .container {
                  background: white;
                  padding: 40px;
                  border-radius: 15px;
                  box-shadow: 0 15px 35px rgba(0,0,0,0.2);
                  width: 100%;
                  max-width: 500px;
              }
              .title {
                  text-align: center;
                  color: #333;
                  margin-bottom: 30px;
                  font-size: 24px;
              }
              .form-group {
                  margin-bottom: 20px;
              }
              label {
                  display: block;
                  margin-bottom: 5px;
                  color: #555;
                  font-weight: bold;
              }
              input {
                  width: 100%;
                  padding: 12px;
                  border: 2px solid #ddd;
                  border-radius: 5px;
                  font-size: 16px;
                  box-sizing: border-box;
              }
              input:focus {
                  border-color: #667eea;
                  outline: none;
              }
              button {
                  width: 100%;
                  padding: 12px;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white;
                  border: none;
                  border-radius: 5px;
                  font-size: 16px;
                  cursor: pointer;
                  font-weight: bold;
              }
              button:hover {
                  opacity: 0.9;
              }
              .message {
                  margin-top: 15px;
                  padding: 10px;
                  border-radius: 5px;
                  text-align: center;
              }
              .success {
                  background: #d4edda;
                  color: #155724;
                  border: 1px solid #c3e6cb;
              }
              .error {
                  background: #f8d7da;
                  color: #721c24;
                  border: 1px solid #f5c6cb;
              }
              .server-info {
                  background: #f8f9fa;
                  padding: 15px;
                  border-radius: 8px;
                  margin-bottom: 20px;
                  border: 1px solid #dee2e6;
              }
          </style>
      </head>
      <body>
          <div class="container">
              <h1 class="title">🔐 اختبار CORS - تسجيل الدخول</h1>
              
              <div class="server-info">
                  <strong>📊 معلومات الخادم:</strong><br>
                  • المنفذ: ${PORT}<br>
                  • CORS: مفعل ✅<br>
                  • الحالة: يعمل بشكل طبيعي<br>
                  • الوقت: ${new Date().toLocaleString('ar-EG')}
              </div>
              
              <form id="loginForm">
                  <div class="form-group">
                      <label for="loginName">اسم المستخدم:</label>
                      <input type="text" id="loginName" name="loginName" value="admin" required>
                  </div>
                  <div class="form-group">
                      <label for="password">كلمة المرور:</label>
                      <input type="password" id="password" name="password" value="admin123456" required>
                  </div>
                  <button type="submit">دخول</button>
              </form>
              <div id="message"></div>
          </div>

          <script>
              document.getElementById('loginForm').addEventListener('submit', async (e) => {
                  e.preventDefault();
                  
                  const loginName = document.getElementById('loginName').value;
                  const password = document.getElementById('password').value;
                  const messageDiv = document.getElementById('message');
                  
                  try {
                      const response = await fetch('http://localhost:${PORT}/api/auth/login', {
                          method: 'POST',
                          headers: {
                              'Content-Type': 'application/json',
                          },
                          body: JSON.stringify({ 
                              loginName, 
                              password, 
                              deviceId: 'test-device-' + Date.now() 
                          })
                      });
                      
                      const data = await response.json();
                      
                      if (data.success) {
                          messageDiv.innerHTML = '<div class="success">✅ ' + data.message + '<br>المستخدم: ' + data.user.username + '<br>الرمز: ' + data.token.substring(0, 20) + '...</div>';
                      } else {
                          messageDiv.innerHTML = '<div class="error">❌ ' + (data.error || 'فشل تسجيل الدخول') + '</div>';
                      }
                  } catch (error) {
                      messageDiv.innerHTML = '<div class="error">❌ خطأ في الاتصال: ' + error.message + '</div>';
                  }
              });
              
              // اختبار الاتصال عند تحميل الصفحة
              fetch('http://localhost:${PORT}/health')
                  .then(response => response.json())
                  .then(data => console.log('✅ Server health check:', data))
                  .catch(error => console.error('❌ Health check failed:', error));
          </script>
      </body>
      </html>
    `);
    return;
  }
  
  // 404 for other paths
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Not found', path }));
});

server.listen(PORT, () => {
  console.log(`🚀 CORS Test Server running on http://localhost:${PORT}`);
  console.log(`🔗 Login page: http://localhost:${PORT}/login`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`📅 Started: ${new Date().toLocaleString('ar-EG')}`);
  console.log(`✅ CORS enabled for all origins`);
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log(`❌ Port ${PORT} is already in use. Try a different port.`);
  } else {
    console.error('❌ Server error:', err);
  }
});

process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down CORS test server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
