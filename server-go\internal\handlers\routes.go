package handlers

import (
	"encoding/json"
	"net/http"
	"yemclient-server/internal/auth"
	"yemclient-server/internal/database"
	"yemclient-server/internal/models"
	"github.com/gorilla/mux"
)

func RegisterRoutes(r *mux.Router) {
	r.Use(auth.AuthMiddleware)

	// Public routes
	r.HandleFunc("/health", healthCheck).Methods("GET")
	r.<PERSON>le<PERSON>un<PERSON>("/api/auth/login", loginHandler).Methods("POST")

	// Protected routes - Users
	r.HandleFunc("/api/users", listUsersHandler).Methods("GET")
	r.HandleFunc("/api/users", createUserHandler).Methods("POST")
	r.HandleFunc("/api/users/{id}", getUserHandler).Methods("GET")
	r.HandleFunc("/api/users/{id}", updateUserHandler).Methods("PUT")
	r.HandleFunc("/api/users/{id}", deleteUserHandler).Methods("DELETE")

	// Protected routes - Clients
	r.<PERSON>le<PERSON>unc("/api/clients", listClientsHandler).Methods("GET")
	r.HandleFunc("/api/clients", createClientHandler).Methods("POST")
	r.HandleFunc("/api/clients/{id}", getClientHandler).Methods("GET")
	r.HandleFunc("/api/clients/{id}", updateClientHandler).Methods("PUT")
	r.HandleFunc("/api/clients/{id}", deleteClientHandler).Methods("DELETE")

	// Protected routes - Agents
	r.HandleFunc("/api/agents", listAgentsHandler).Methods("GET")
	r.HandleFunc("/api/agents", createAgentHandler).Methods("POST")
	r.HandleFunc("/api/agents/{id}", getAgentHandler).Methods("GET")
	r.HandleFunc("/api/agents/{id}", updateAgentHandler).Methods("PUT")
	r.HandleFunc("/api/agents/{id}", deleteAgentHandler).Methods("DELETE")

	// Protected routes - Data Records
	r.HandleFunc("/api/records", listRecordsHandler).Methods("GET")
	r.HandleFunc("/api/records", createRecordHandler).Methods("POST")
	r.HandleFunc("/api/records/{id}", getRecordHandler).Methods("GET")
	r.HandleFunc("/api/records/{id}", updateRecordHandler).Methods("PUT")
	r.HandleFunc("/api/records/{id}", deleteRecordHandler).Methods("DELETE")

	// Client specific routes
	r.HandleFunc("/api/client/login", clientLoginHandler).Methods("POST")
	r.HandleFunc("/api/client/dashboard", clientDashboardHandler).Methods("GET")

	// Admin routes
	r.HandleFunc("/api/admin/stats", adminStatsHandler).Methods("GET")
	r.HandleFunc("/api/admin/activity", recentActivityHandler).Methods("GET")
}

func loginHandler(w http.ResponseWriter, r *http.Request) {
	var creds struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := json.NewDecoder(r.Body).Decode(&creds); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	var user models.User
	if err := database.DB.Where("username = ?", creds.Username).First(&user).Error; err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	if !auth.CheckPasswordHash(creds.Password, user.Password) {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	token, err := auth.GenerateToken(user.ID)
	if err != nil {
		http.Error(w, "Failed to generate token", http.StatusInternalServerError)
		return
	}

	// Don't return password hash in response
	user.Password = ""
	response := map[string]interface{}{
		"token": token,
		"user":  user,
	}

	json.NewEncoder(w).Encode(response)
}

func clientLoginHandler(w http.ResponseWriter, r *http.Request) {
	// TODO: Implement client login logic
	w.WriteHeader(http.StatusNotImplemented)
	w.Write([]byte(`{"message":"Client login handler not implemented"}`))
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"OK"}`))
}
