@echo off
chcp 65001 >nul
echo.
echo ========================================
echo     تشغيل شامل - خادم + صفحة اختبار
echo ========================================
echo.

echo 🚀 تشغيل الخادم...
echo    فحص المنفذ 8080...
netstat -an | find "8080" >nul
if %errorlevel%==0 (
    echo    ⚠️ إيقاف العمليات الموجودة على المنفذ 8080...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 1 /nobreak >nul
)

start "Yemen Client Server" cmd /k "cd server && node working-server.js"

echo ⏳ انتظار 3 ثوان لبدء الخادم...
timeout /t 3 /nobreak >nul

echo 🌐 فتح صفحة الاختبار...
start "" "test-api-web.html"

echo.
echo ✅ تم التشغيل بنجاح!
echo.
echo 📋 ما تم فتحه:
echo    1. خادم Yemen Client على المنفذ 8080
echo    2. صفحة اختبار API في المتصفح
echo.
echo 🎯 الآن يمكنك:
echo    - اختبار API من صفحة الويب
echo    - مراقبة السجلات في نافذة الخادم
echo    - استخدام الاختبارات السريعة أو المخصصة
echo.
echo 🌐 الروابط:
echo    - الخادم المحلي: http://localhost:8080
echo    - الخادم الخارجي: http://***********:8080
echo    - API المطورين: http://***********:8080/api/external/verify-direct
echo.
pause
