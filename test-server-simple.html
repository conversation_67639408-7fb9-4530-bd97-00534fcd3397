<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المطورين</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار API المطورين</h1>
        
        <div class="test-section">
            <h3>🌐 اختبار Health Check</h3>
            <button class="btn" onclick="testHealthCheck()">اختبار Health Check</button>
            <div id="health-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔐 اختبار API التحقق المباشر (النموذج القديم)</h3>
            <button class="btn" onclick="testOldFormat()">اختبار النموذج القديم</button>
            <div id="old-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔐 اختبار API التحقق المباشر (النموذج الجديد)</h3>
            <button class="btn" onclick="testNewFormat()">اختبار النموذج الجديد</button>
            <div id="new-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>❌ اختبار بيانات خاطئة</h3>
            <button class="btn" onclick="testInvalidData()">اختبار بيانات خاطئة</button>
            <div id="invalid-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://***********:8080';
        
        function showResult(elementId, data, isSuccess) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }
        
        async function testHealthCheck() {
            try {
                const response = await fetch(`${BASE_URL}/api/external/health`);
                const data = await response.json();
                showResult('health-result', `Status: ${response.status}\n\n${JSON.stringify(data, null, 2)}`, response.ok);
            } catch (error) {
                showResult('health-result', `خطأ في الاتصال: ${error.message}`, false);
            }
        }
        
        async function testOldFormat() {
            try {
                const response = await fetch(`${BASE_URL}/api/external/verify-direct`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_login_name: 'testuser',
                        agent_login_password: 'test123',
                        client_code: 1004,
                        client_token: 'TEST1004'
                    })
                });
                const data = await response.json();
                showResult('old-result', `Status: ${response.status}\n\n${JSON.stringify(data, null, 2)}`, response.ok);
            } catch (error) {
                showResult('old-result', `خطأ في الاتصال: ${error.message}`, false);
            }
        }
        
        async function testNewFormat() {
            try {
                const response = await fetch(`${BASE_URL}/api/external/verify-direct`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'testuser',
                        password: 'test123',
                        clientCode: 1004,
                        clientToken: 'TEST1004',
                        deviceId: 'DEV001'
                    })
                });
                const data = await response.json();
                showResult('new-result', `Status: ${response.status}\n\n${JSON.stringify(data, null, 2)}`, response.ok);
            } catch (error) {
                showResult('new-result', `خطأ في الاتصال: ${error.message}`, false);
            }
        }
        
        async function testInvalidData() {
            try {
                const response = await fetch(`${BASE_URL}/api/external/verify-direct`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'wronguser',
                        password: 'wrongpass',
                        clientCode: 9999,
                        clientToken: 'WRONG'
                    })
                });
                const data = await response.json();
                showResult('invalid-result', `Status: ${response.status}\n\n${JSON.stringify(data, null, 2)}`, false);
            } catch (error) {
                showResult('invalid-result', `خطأ في الاتصال: ${error.message}`, false);
            }
        }
    </script>
</body>
</html>
