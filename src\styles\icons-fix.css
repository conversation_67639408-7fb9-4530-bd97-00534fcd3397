/* إصلاح مشكلة الأيقونات */

/* التأكد من تحميل خطوط Material Icons */
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');
@import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200');

/* إصلاح عرض الأيقونات */
.MuiSvgIcon-root {
  display: inline-block !important;
  font-size: 1.5rem !important;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms !important;
  flex-shrink: 0 !important;
  user-select: none !important;
}

/* إصلاح أزرار الأيقونات */
.MuiIconButton-root {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  box-sizing: border-box !important;
  background-color: transparent !important;
  outline: 0 !important;
  border: 0 !important;
  margin: 0 !important;
  border-radius: 50% !important;
  padding: 8px !important;
  cursor: pointer !important;
  user-select: none !important;
  vertical-align: middle !important;
  text-decoration: none !important;
  color: inherit !important;
  font-size: 1.5rem !important;
}

/* إصلاح أزرار مع أيقونات */
.MuiButton-startIcon {
  display: inherit !important;
  margin-right: 8px !important;
  margin-left: -4px !important;
}

.MuiButton-startIcon > *:nth-of-type(1) {
  font-size: 20px !important;
}

/* إصلاح عرض الأيقونات في الجداول */
.MuiDataGrid-root .MuiIconButton-root {
  padding: 4px !important;
}

.MuiDataGrid-root .MuiSvgIcon-root {
  font-size: 1.2rem !important;
}

/* إصلاح Tooltip */
.MuiTooltip-tooltip {
  font-size: 0.75rem !important;
  max-width: 300px !important;
  word-wrap: break-word !important;
}

/* إصلاح الأيقونات المفقودة - عرض نص بديل */
.MuiSvgIcon-root:empty::before {
  content: "⚙️";
  font-size: 1.2em;
  display: inline-block;
}

/* أيقونات بديلة محددة */
.MuiSvgIcon-root[data-testid="AddIcon"]:empty::before {
  content: "➕";
}

.MuiSvgIcon-root[data-testid="EditIcon"]:empty::before {
  content: "✏️";
}

.MuiSvgIcon-root[data-testid="DeleteIcon"]:empty::before {
  content: "🗑️";
}

.MuiSvgIcon-root[data-testid="VisibilityIcon"]:empty::before {
  content: "👁️";
}

.MuiSvgIcon-root[data-testid="SearchIcon"]:empty::before {
  content: "🔍";
}

.MuiSvgIcon-root[data-testid="PersonIcon"]:empty::before {
  content: "👤";
}

.MuiSvgIcon-root[data-testid="BusinessIcon"]:empty::before {
  content: "🏢";
}

.MuiSvgIcon-root[data-testid="GroupIcon"]:empty::before {
  content: "👥";
}

.MuiSvgIcon-root[data-testid="DashboardIcon"]:empty::before {
  content: "📊";
}

.MuiSvgIcon-root[data-testid="SecurityIcon"]:empty::before {
  content: "🔒";
}

.MuiSvgIcon-root[data-testid="SettingsIcon"]:empty::before {
  content: "⚙️";
}

.MuiSvgIcon-root[data-testid="ExitToAppIcon"]:empty::before {
  content: "🚪";
}

/* إصلاح عام للأيقونات */
.icon-fallback {
  display: inline-block;
  font-size: 1.2em;
  line-height: 1;
  vertical-align: middle;
}

/* تحسين عرض الأزرار */
.MuiButton-root {
  min-height: 36px !important;
}

.MuiButton-contained {
  box-shadow: 0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12) !important;
}

.MuiButton-contained:hover {
  box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12) !important;
}

/* إصلاح الخطوط العربية */
body {
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif !important;
  direction: rtl !important;
}

/* إصلاح عرض النصوص مع الأيقونات */
.button-with-icon {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.button-with-icon .icon {
  font-size: 1.2em !important;
  line-height: 1 !important;
}

.button-with-icon .text {
  font-weight: 500 !important;
}
