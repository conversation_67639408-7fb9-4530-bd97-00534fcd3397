@echo off
chcp 65001 >nul
title تشغيل اختبارات النظام

color 0B
echo ========================================
echo      تشغيل اختبارات النظام
echo ========================================

echo.
echo 🧪 اختر نوع الاختبار:
echo.
echo 1. اختبار شامل للنظام
echo 2. اختبار الخادم فقط
echo 3. اختبار API الوكلاء
echo 4. اختبار قاعدة البيانات
echo 5. اختبار واجهة العميل
echo 6. اختبار الأمان
echo 7. تشغيل جميع الاختبارات
echo 8. خروج
echo.

set /p choice="اختر رقم (1-8): "

if "%choice%"=="1" goto comprehensive_test
if "%choice%"=="2" goto server_test
if "%choice%"=="3" goto api_test
if "%choice%"=="4" goto database_test
if "%choice%"=="5" goto client_test
if "%choice%"=="6" goto security_test
if "%choice%"=="7" goto all_tests
if "%choice%"=="8" goto exit
goto invalid_choice

:comprehensive_test
echo.
echo 🔍 تشغيل الاختبار الشامل...
node test-remote-agent.js
goto end

:server_test
echo.
echo 🖥️ اختبار الخادم...
echo فحص حالة الخادم...
curl -s http://localhost:8081/api/external/health
if %errorlevel% equ 0 (
    echo ✅ الخادم يعمل بشكل صحيح
) else (
    echo ❌ الخادم لا يستجيب
)
goto end

:api_test
echo.
echo 🔌 اختبار API الوكلاء...
echo.
echo اختبار النموذج الجديد...
curl -X POST http://localhost:8081/api/external/verify-direct ^
  -H "Content-Type: application/json" ^
  -d "{\"username\": \"testuser\", \"password\": \"test123\", \"clientCode\": 1004, \"clientToken\": \"TEST1004\"}"

echo.
echo.
echo اختبار النموذج القديم...
curl -X POST http://localhost:8081/api/external/verify-direct ^
  -H "Content-Type: application/json" ^
  -d "{\"agent_login_name\": \"testuser\", \"agent_login_password\": \"test123\", \"client_code\": 1004, \"client_token\": \"TEST1004\"}"
goto end

:database_test
echo.
echo 🗄️ اختبار قاعدة البيانات...
echo فحص الاتصال بقاعدة البيانات...
psql -U postgres -d yemclient_db -c "SELECT COUNT(*) FROM users;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ قاعدة البيانات متصلة
) else (
    echo ❌ فشل في الاتصال بقاعدة البيانات
)
goto end

:client_test
echo.
echo 🌐 اختبار واجهة العميل...
echo فحص توفر واجهة العميل...
curl -s http://localhost:5173 >nul
if %errorlevel% equ 0 (
    echo ✅ واجهة العميل متاحة
) else (
    echo ❌ واجهة العميل غير متاحة
    echo تأكد من تشغيل العميل: cd client && npm run dev
)
goto end

:security_test
echo.
echo 🔒 اختبار الأمان...
echo.
echo اختبار حماية SQL Injection...
curl -X POST http://localhost:8081/api/external/verify-direct ^
  -H "Content-Type: application/json" ^
  -d "{\"username\": \"'; DROP TABLE users; --\", \"password\": \"test123\", \"clientCode\": 1004, \"clientToken\": \"TEST1004\"}"

echo.
echo.
echo اختبار Rate Limiting...
for /l %%i in (1,1,10) do (
    curl -s http://localhost:8081/api/external/health >nul
    echo طلب %%i
)
goto end

:all_tests
echo.
echo 🚀 تشغيل جميع الاختبارات...
echo.

echo 1️⃣ اختبار متطلبات النظام...
node --version
npm --version
psql --version 2>nul

echo.
echo 2️⃣ اختبار الخادم...
curl -s http://localhost:8081/api/external/health

echo.
echo 3️⃣ اختبار قاعدة البيانات...
psql -U postgres -d yemclient_db -c "SELECT 'Database OK' as status;" 2>nul

echo.
echo 4️⃣ اختبار API الوكلاء...
curl -X POST http://localhost:8081/api/external/verify-direct ^
  -H "Content-Type: application/json" ^
  -d "{\"username\": \"testuser\", \"password\": \"test123\", \"clientCode\": 1004, \"clientToken\": \"TEST1004\"}"

echo.
echo 5️⃣ اختبار واجهة العميل...
curl -s http://localhost:5173 >nul

echo.
echo 6️⃣ تشغيل الاختبار الشامل...
node test-remote-agent.js

goto end

:invalid_choice
echo ❌ اختيار غير صحيح!
timeout /t 2 /nobreak >nul
goto start

:exit
echo 👋 خروج...
goto end

:start
goto comprehensive_test

:end
echo.
echo ========================================
echo.
echo 📋 نصائح مفيدة:
echo - إذا فشل اختبار، تحقق من تشغيل الخدمة المطلوبة
echo - راجع ملف دليل-تشغيل-Remote-Agent.md للمساعدة
echo - تحقق من سجلات الخادم في نافذة الأوامر
echo.
pause
