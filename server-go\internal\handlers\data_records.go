package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"
	
	"github.com/gorilla/mux"
	"yemclient-server/internal/database"
	"yemclient-server/internal/models"
)

func GetDataRecordsHandler(w http.ResponseWriter, r *http.Request) {
	var records []models.DataRecord
	if err := database.DB.Find(&records).Error; err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(records)
}

func CreateDataRecordHandler(w http.ResponseWriter, r *http.Request) {
	var record models.DataRecord
	if err := json.NewDecoder(r.Body).Decode(&record); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	
	if err := database.DB.Create(&record).Error; err != nil {
		http.Error(w, err.<PERSON>rror(), http.StatusInternalServerError)
		return
	}
	
	w.Write<PERSON>eader(http.StatusCreated)
	json.NewEncoder(w).Encode(record)
}

func UpdateDataRecordHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid record ID", http.StatusBadRequest)
		return
	}
	
	var record models.DataRecord
	if err := database.DB.First(&record, id).Error; err != nil {
		http.Error(w, "Record not found", http.StatusNotFound)
		return
	}
	
	if err := json.NewDecoder(r.Body).Decode(&record); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	
	if err := database.DB.Save(&record).Error; err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	json.NewEncoder(w).Encode(record)
}

func DeleteDataRecordHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid record ID", http.StatusBadRequest)
		return
	}
	
	if err := database.DB.Delete(&models.DataRecord{}, id).Error; err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	w.WriteHeader(http.StatusNoContent)
}
