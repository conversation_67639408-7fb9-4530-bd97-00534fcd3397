@echo off
chcp 65001 >nul
title فحص وتشغيل الخادم - YemClient
color 0A
echo.
echo ========================================
echo           فحص وتشغيل الخادم
echo ========================================
echo.
echo 🔄 جاري الفحص...

echo 🔍 فحص المنفذ 8080...
netstat -an | find "8080" >nul 2>&1
if %errorlevel%==0 (
    echo ✅ يوجد خادم يعمل على المنفذ 8080
    echo.
    echo 🧪 اختبار API...
    echo.
    powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8080/api/external/verify-direct' -Method Post -ContentType 'application/json' -Body '{\"agent_login_name\":\"testuser\",\"agent_login_password\":\"test123\",\"client_code\":\"1000\",\"client_token\":\"ABC12345\"}'; Write-Host '✅ API يعمل بشكل صحيح:'; $response | ConvertTo-Json } catch { Write-Host '❌ خطأ في API:' $_.Exception.Message }"
    echo.
    echo ✅ إذا ظهر JSON أعلاه، فالخادم يعمل بشكل صحيح
) else (
    echo ❌ لا يوجد خادم يعمل على المنفذ 8080
    echo.
    echo 🛑 إيقاف أي عمليات متضاربة...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    echo.
    echo 🚀 تشغيل الخادم...
    cd server
    echo 📂 في مجلد: %cd%
    echo 🎯 تشغيل: node working-server.js
    start "YemClient Server" cmd /k "node working-server.js"
    echo.
    echo ⏳ انتظار تشغيل الخادم...
    timeout /t 8 >nul
    echo.
    echo 🧪 اختبار API بعد التشغيل...
    powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8080/api/external/verify-direct' -Method Post -ContentType 'application/json' -Body '{\"agent_login_name\":\"testuser\",\"agent_login_password\":\"test123\",\"client_code\":\"1000\",\"client_token\":\"ABC12345\"}'; Write-Host '✅ API يعمل بشكل صحيح:'; $response | ConvertTo-Json } catch { Write-Host '❌ خطأ في API:' $_.Exception.Message }"
)

echo.
echo.
echo ========================================
echo                النتيجة
echo ========================================
echo 📋 تعليمات:
echo - إذا ظهر JSON صحيح أعلاه، فالخادم يعمل
echo - إذا ظهر HTML أو خطأ، فهناك مشكلة في الخادم
echo - يمكنك الآن اختبار صفحة المطورين
echo - إذا لم يعمل، جرب تشغيل الخادم يدوياً: cd server ^&^& node working-server.js
echo.
echo ⏸️ اضغط أي مفتاح للإغلاق...
pause >nul
