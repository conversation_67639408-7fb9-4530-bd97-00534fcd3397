/* إضافة خط Khalid-Art-bold */
@font-face {
  font-family: '<PERSON>-Art-bold';
  src: url('/fonts/Khalid-Art-bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

/* إعدادات عامة للواجهة العربية */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Khalid-Art-bold', 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  text-align: right;
  background-color: #f8f9fa;
  font-weight: bold;
}

/* إعدادات الخطوط العربية */
.arabic-text {
  font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
  direction: rtl;
  text-align: right;
}

/* إعدادات التخطيط العربي */
.rtl-layout {
  direction: rtl;
}

.ltr-layout {
  direction: ltr;
}

/* تحسينات للجداول العربية */
.MuiTableCell-root {
  text-align: right !important;
  direction: rtl;
}

.MuiTableCell-head {
  font-weight: 600 !important;
}

/* تحسينات للنماذج العربية */
.MuiTextField-root {
  direction: rtl;
}

.MuiInputBase-input {
  text-align: right;
  direction: rtl;
}

/* تحسينات للأزرار العربية */
.MuiButton-root {
  font-family: inherit;
}

/* تحسينات للقوائم العربية */
.MuiList-root {
  direction: rtl;
}

.MuiListItemText-root {
  text-align: right;
}

/* تحسينات للحوارات العربية */
.MuiDialog-paper {
  direction: rtl;
}

.MuiDialogTitle-root {
  text-align: right;
}

.MuiDialogContent-root {
  direction: rtl;
  text-align: right;
}

/* تحسينات للتنبيهات العربية */
.MuiAlert-root {
  direction: rtl;
  text-align: right;
}

/* تحسينات للبطاقات العربية */
.MuiCard-root {
  direction: rtl;
}

.MuiCardContent-root {
  text-align: right;
}

/* تحسينات للشرائح العربية */
.MuiChip-root {
  direction: rtl;
}

/* تحسينات للتبديل العربي */
.MuiTabs-root {
  direction: rtl;
}

.MuiTab-root {
  text-align: right;
}

/* تحسينات للتنقل العربي */
.MuiPagination-root {
  direction: ltr; /* الأرقام تبقى من اليسار لليمين */
}

/* تحسينات للقائمة المنسدلة العربية */
.MuiSelect-root {
  text-align: right;
  direction: rtl;
}

.MuiMenuItem-root {
  text-align: right;
  direction: rtl;
}

/* تحسينات للتقويم العربي */
.MuiDatePicker-root {
  direction: rtl;
}

/* تحسينات للشريط الجانبي العربي */
.MuiDrawer-paper {
  direction: rtl;
}

/* تحسينات للشريط العلوي العربي */
.MuiAppBar-root {
  direction: rtl;
}

.MuiToolbar-root {
  direction: rtl;
}

/* تحسينات للأيقونات العربية */
.MuiSvgIcon-root {
  transform: scaleX(-1); /* عكس الأيقونات للاتجاه العربي */
}

/* استثناءات للأيقونات التي لا تحتاج عكس */
.no-flip .MuiSvgIcon-root,
.MuiSvgIcon-root.no-flip {
  transform: none;
}

/* تحسينات للنصوص الطويلة */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* تحسينات للمسافات العربية */
.arabic-spacing {
  letter-spacing: 0.5px;
}

/* تحسينات للألوان */
.primary-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.secondary-gradient {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* تحسينات للظلال */
.card-shadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hover-shadow {
  transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* تحسينات للانتقالات */
.smooth-transition {
  transition: all 0.3s ease;
}

/* تحسينات للتمرير */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #ccc #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* تحسينات للطباعة */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    direction: rtl;
    font-size: 12pt;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }

  .MuiContainer-root {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
  .hide-desktop {
    display: none !important;
  }
}
