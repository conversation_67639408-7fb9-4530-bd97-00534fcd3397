@echo off
chcp 65001 >nul
color 0E
title إصلاح المشكلة الأمنية العاجلة

echo.
echo ========================================
echo     🚨 إصلاح المشكلة الأمنية العاجلة
echo ========================================
echo.

echo ⚠️ تحذير: سيتم إيقاف جميع خوادم Node.js وتشغيل الخادم المحمي
echo.
pause

echo.
echo 🛑 الخطوة 1: إيقاف جميع خوادم Node.js...
echo ================================================
taskkill /F /IM node.exe /T 2>nul
if %errorlevel%==0 (
    echo ✅ تم إيقاف خوادم Node.js
) else (
    echo ℹ️ لم توجد خوادم Node.js للإيقاف
)

echo.
echo 🛑 الخطوة 2: إيقاف العمليات على المنفذ 8080...
echo ================================================
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
    echo إيقاف العملية %%a...
    taskkill /PID %%a /F 2>nul
)

echo.
echo ⏳ انتظار 3 ثوان...
timeout /t 3 /nobreak >nul

echo.
echo 🔍 الخطوة 3: التحقق من إيقاف العمليات...
echo ================================================
netstat -ano | findstr :8080 >nul 2>&1
if %errorlevel%==0 (
    echo ⚠️ لا تزال هناك عمليات على المنفذ 8080:
    netstat -ano | findstr :8080
    echo.
    echo 🔄 محاولة إيقاف إضافية...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
        echo إيقاف العملية %%a بالقوة...
        taskkill /PID %%a /F 2>nul
    )
    timeout /t 2 /nobreak >nul
) else (
    echo ✅ تم إيقاف جميع العمليات على المنفذ 8080
)

echo.
echo 🚀 الخطوة 4: تشغيل الخادم المحمي...
echo ================================================
echo 📍 الخادم: http://localhost:8080
echo 🌐 خارجي: http://***********:8080
echo 🛡️ مع حماية JWT كاملة
echo.

if not exist "server\working-server.js" (
    echo ❌ خطأ: ملف working-server.js غير موجود!
    echo 📁 تحقق من مجلد server
    pause
    exit /b 1
)

echo 🎯 بدء تشغيل الخادم المحمي...
echo.
echo ⚠️ ملاحظة: اتركه يعمل ولا تغلق هذه النافذة
echo 🔒 جميع /api/* محمية الآن بـ JWT Token
echo ✅ APIs العامة فقط: /api/auth/login
echo.
echo ========================================
echo      🛡️ الخادم المحمي يعمل الآن
echo ========================================
echo.

cd server
node working-server.js

echo.
echo 🛑 تم إيقاف الخادم
echo.
pause
