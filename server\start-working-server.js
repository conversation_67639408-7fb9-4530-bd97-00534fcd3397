// تشغيل working-server.js مباشرة
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 بدء تشغيل working-server.js...');
console.log('📁 المجلد الحالي:', __dirname);

// تشغيل working-server.js
const serverProcess = spawn('node', ['working-server.js'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

serverProcess.on('error', (err) => {
  console.error('❌ خطأ في تشغيل الخادم:', err);
});

serverProcess.on('close', (code) => {
  console.log(`🛑 انتهى الخادم برمز: ${code}`);
});

// التعامل مع إيقاف العملية
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 إنهاء الخادم...');
  serverProcess.kill('SIGTERM');
  process.exit(0);
});
