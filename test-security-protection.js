const axios = require('axios');

const BASE_URL = 'http://localhost:8080';

console.log('🔒 اختبار حماية API - نظام إدارة العملاء اليمني');
console.log('='.repeat(60));

async function testAPIProtection() {
  try {
    console.log('\n1️⃣ اختبار الوصول بدون Token (يجب أن يفشل):');
    console.log('GET /api/users');
    
    try {
      const response = await axios.get(`${BASE_URL}/api/users`);
      console.log('❌ خطر أمني! API غير محمي - تم الوصول بدون token');
      console.log('البيانات المكشوفة:', response.data);
      return false;
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ ممتاز! API محمي - تم رفض الوصول بدون token');
        console.log('رسالة الحماية:', error.response.data.message);
      } else {
        console.log('❓ خطأ غير متوقع:', error.message);
      }
    }

    console.log('\n2️⃣ اختبار تسجيل الدخول للحصول على Token:');
    
    const loginData = {
      loginName: 'admin',
      password: 'admin123456',
      deviceId: 'test_security_device_123'
    };

    console.log('POST /api/auth/login');
    console.log('البيانات:', { ...loginData, password: '***' });

    let token = null;
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, loginData);
      
      if (loginResponse.data.success && loginResponse.data.token) {
        token = loginResponse.data.token;
        console.log('✅ تم تسجيل الدخول بنجاح');
        console.log('Token Type:', loginResponse.data.tokenType);
        console.log('Expires In:', loginResponse.data.expiresIn);
        console.log('Token (أول 50 حرف):', token.substring(0, 50) + '...');
      } else {
        console.log('❌ فشل تسجيل الدخول:', loginResponse.data);
        return false;
      }
    } catch (error) {
      console.log('❌ خطأ في تسجيل الدخول:', error.response?.data || error.message);
      return false;
    }

    console.log('\n3️⃣ اختبار الوصول مع Token صحيح:');
    console.log('GET /api/users مع Bearer Token');

    try {
      const response = await axios.get(`${BASE_URL}/api/users`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ تم الوصول بنجاح مع Token صحيح');
      console.log('عدد المستخدمين:', response.data.data?.length || 0);
      console.log('إجمالي السجلات:', response.data.total || 0);
    } catch (error) {
      console.log('❌ فشل الوصول مع Token:', error.response?.data || error.message);
      return false;
    }

    console.log('\n4️⃣ اختبار الوصول مع Token خاطئ:');
    console.log('GET /api/users مع Bearer Token خاطئ');

    try {
      const response = await axios.get(`${BASE_URL}/api/users`, {
        headers: {
          'Authorization': 'Bearer invalid_token_123'
        }
      });
      
      console.log('❌ خطر أمني! تم قبول Token خاطئ');
      console.log('البيانات:', response.data);
      return false;
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ ممتاز! تم رفض Token خاطئ');
        console.log('رسالة الخطأ:', error.response.data.message);
      } else {
        console.log('❓ خطأ غير متوقع:', error.message);
      }
    }

    console.log('\n5️⃣ اختبار APIs العامة (يجب أن تعمل بدون Token):');
    
    const publicAPIs = [
      '/api/external/health'
    ];

    for (const api of publicAPIs) {
      try {
        console.log(`GET ${api}`);
        const response = await axios.get(`${BASE_URL}${api}`);
        console.log(`✅ ${api} - يعمل بدون token (صحيح)`);
      } catch (error) {
        console.log(`❌ ${api} - خطأ: ${error.response?.status || error.message}`);
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎉 اكتمل اختبار الحماية بنجاح!');
    console.log('✅ API محمي بشكل صحيح');
    console.log('✅ JWT Token يعمل بشكل صحيح');
    console.log('✅ APIs العامة متاحة بدون token');
    console.log('✅ تم منع الوصول غير المصرح به');
    
    return true;

  } catch (error) {
    console.error('❌ خطأ في اختبار الحماية:', error.message);
    return false;
  }
}

// تشغيل الاختبار
testAPIProtection().then(success => {
  if (success) {
    console.log('\n🔒 النظام آمن ومحمي بشكل صحيح!');
    process.exit(0);
  } else {
    console.log('\n⚠️ يوجد مشاكل أمنية تحتاج إصلاح!');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ فشل الاختبار:', error.message);
  process.exit(1);
});
