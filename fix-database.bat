@echo off
chcp 65001 >nul
cls
echo.
echo ========================================
echo   Fix Database Connection - hash8080
echo ========================================
echo.

echo 🔧 Fixing database connection and user setup...
echo.

cd /d "%~dp0server"
if %errorlevel% neq 0 (
    echo ❌ Error: Cannot access server directory
    echo.
    echo Trying from root directory...
    cd /d "%~dp0"
    node fix-database-connection.js
) else (
    echo ✅ In server directory
    cd ..
    node fix-database-connection.js
)

echo.
echo ========================================
echo         Fix Complete
echo ========================================
echo.
echo Press any key to close...
pause >nul
