import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Divider,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
  Container
} from '@mui/material'
import { useAuth } from '../contexts/AuthContext'
import { useSnackbar } from 'notistack'

const ClientLoginTest = () => {
  const [testData, setTestData] = useState({
    clientCode: '1001',
    password: 'Hash2020@'
  })
  const [logs, setLogs] = useState([])
  const [loading, setLoading] = useState(false)
  const [testResults, setTestResults] = useState(null)

  const { clientLogin } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, { message, type, timestamp }])
    console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`)
  }

  const clearLogs = () => {
    setLogs([])
    setTestResults(null)
  }

  const testDirectAPI = async () => {
    addLog('🧪 بدء اختبار API مباشر...', 'info')
    
    try {
      const response = await fetch('/api/client/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientCode: parseInt(testData.clientCode),
          password: testData.password
        })
      })

      addLog(`📡 استجابة API: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error')
      
      const data = await response.json()
      addLog(`📊 بيانات الاستجابة: ${JSON.stringify(data, null, 2)}`, 'info')

      return { response, data }
    } catch (error) {
      addLog(`❌ خطأ في API مباشر: ${error.message}`, 'error')
      return { error }
    }
  }

  const testAuthContext = async () => {
    addLog('🔐 بدء اختبار AuthContext...', 'info')
    
    try {
      if (!clientLogin) {
        addLog('❌ دالة clientLogin غير موجودة في AuthContext', 'error')
        return { error: 'clientLogin not found' }
      }

      addLog('✅ دالة clientLogin موجودة في AuthContext', 'success')
      
      const result = await clientLogin(testData.clientCode, testData.password)
      addLog(`📊 نتيجة AuthContext: ${JSON.stringify(result, null, 2)}`, result.success ? 'success' : 'error')

      return result
    } catch (error) {
      addLog(`❌ خطأ في AuthContext: ${error.message}`, 'error')
      return { error: error.message }
    }
  }

  const testLocalStorage = () => {
    addLog('💾 فحص localStorage...', 'info')
    
    const clientData = localStorage.getItem('clientData')
    if (clientData) {
      try {
        const parsed = JSON.parse(clientData)
        addLog(`✅ بيانات العميل في localStorage: ${JSON.stringify(parsed, null, 2)}`, 'success')
        return parsed
      } catch (error) {
        addLog(`❌ خطأ في تحليل بيانات localStorage: ${error.message}`, 'error')
        return null
      }
    } else {
      addLog('⚠️ لا توجد بيانات عميل في localStorage', 'warning')
      return null
    }
  }

  const runFullTest = async () => {
    setLoading(true)
    clearLogs()
    
    addLog('🚀 بدء الاختبار الشامل لدخول العميل...', 'info')
    addLog(`📝 بيانات الاختبار: رمز العميل ${testData.clientCode}, كلمة المرور: ${testData.password}`, 'info')

    // اختبار 1: API مباشر
    addLog('--- اختبار 1: API مباشر ---', 'info')
    const directResult = await testDirectAPI()

    // اختبار 2: AuthContext
    addLog('--- اختبار 2: AuthContext ---', 'info')
    const authResult = await testAuthContext()

    // اختبار 3: localStorage
    addLog('--- اختبار 3: localStorage ---', 'info')
    const storageResult = testLocalStorage()

    // تحليل النتائج
    addLog('--- تحليل النتائج ---', 'info')
    
    const results = {
      directAPI: directResult,
      authContext: authResult,
      localStorage: storageResult,
      summary: {
        directAPIWorks: directResult.response?.ok && directResult.data?.success,
        authContextWorks: authResult.success,
        localStorageHasData: !!storageResult
      }
    }

    setTestResults(results)

    if (results.summary.directAPIWorks && results.summary.authContextWorks) {
      addLog('🎉 جميع الاختبارات نجحت! المشكلة قد تكون في الواجهة', 'success')
      enqueueSnackbar('جميع الاختبارات نجحت!', { variant: 'success' })
    } else {
      addLog('❌ بعض الاختبارات فشلت. راجع التفاصيل أعلاه', 'error')
      enqueueSnackbar('بعض الاختبارات فشلت', { variant: 'error' })
    }

    setLoading(false)
  }

  const testOtherClients = async () => {
    const clients = [
      { code: 1000, password: '112223333', name: 'محمد علي الحاشدي' },
      { code: 1002, password: 'password123', name: 'ون كاش' },
      { code: 1003, password: 'test123', name: 'شركة اسي' }
    ]

    addLog('🧪 اختبار عملاء آخرين...', 'info')

    for (const client of clients) {
      addLog(`--- اختبار العميل ${client.code} (${client.name}) ---`, 'info')
      
      try {
        const response = await fetch('/api/client/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            clientCode: client.code,
            password: client.password
          })
        })

        const data = await response.json()
        
        if (response.ok && data.success) {
          addLog(`✅ العميل ${client.code} نجح`, 'success')
        } else {
          addLog(`❌ العميل ${client.code} فشل: ${data.message}`, 'error')
        }
      } catch (error) {
        addLog(`❌ خطأ في اختبار العميل ${client.code}: ${error.message}`, 'error')
      }
    }
  }

  const handleChange = (e) => {
    setTestData({
      ...testData,
      [e.target.name]: e.target.value
    })
  }

  const getLogColor = (type) => {
    switch (type) {
      case 'success': return 'success'
      case 'error': return 'error'
      case 'warning': return 'warning'
      default: return 'info'
    }
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom sx={{ textAlign: 'center', mb: 4 }}>
        🧪 صفحة اختبار دخول العميل
      </Typography>

      <Grid container spacing={3}>
        {/* بيانات الاختبار */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📝 بيانات الاختبار
              </Typography>
              
              <TextField
                fullWidth
                label="رمز العميل"
                name="clientCode"
                value={testData.clientCode}
                onChange={handleChange}
                margin="normal"
                type="number"
              />
              
              <TextField
                fullWidth
                label="كلمة المرور"
                name="password"
                value={testData.password}
                onChange={handleChange}
                margin="normal"
                type="password"
              />

              <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={runFullTest}
                  disabled={loading}
                  fullWidth
                >
                  🚀 تشغيل الاختبار الشامل
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={testOtherClients}
                  disabled={loading}
                  fullWidth
                >
                  🧪 اختبار عملاء آخرين
                </Button>
                
                <Button
                  variant="text"
                  onClick={clearLogs}
                  fullWidth
                >
                  🗑️ مسح السجلات
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* ملخص النتائج */}
          {testResults && (
            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 ملخص النتائج
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Chip
                    label={`API مباشر: ${testResults.summary.directAPIWorks ? 'نجح' : 'فشل'}`}
                    color={testResults.summary.directAPIWorks ? 'success' : 'error'}
                    size="small"
                  />
                  <Chip
                    label={`AuthContext: ${testResults.summary.authContextWorks ? 'نجح' : 'فشل'}`}
                    color={testResults.summary.authContextWorks ? 'success' : 'error'}
                    size="small"
                  />
                  <Chip
                    label={`localStorage: ${testResults.summary.localStorageHasData ? 'يحتوي بيانات' : 'فارغ'}`}
                    color={testResults.summary.localStorageHasData ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* سجل الأحداث */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📋 سجل الأحداث والتشخيص
              </Typography>
              
              <Paper
                sx={{
                  maxHeight: 600,
                  overflow: 'auto',
                  p: 2,
                  backgroundColor: '#f5f5f5'
                }}
              >
                {logs.length === 0 ? (
                  <Typography color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                    لا توجد سجلات بعد. اضغط "تشغيل الاختبار الشامل" للبدء
                  </Typography>
                ) : (
                  <List dense>
                    {logs.map((log, index) => (
                      <ListItem key={index} sx={{ py: 0.5 }}>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip
                                label={log.timestamp}
                                size="small"
                                variant="outlined"
                              />
                              <Chip
                                label={log.type.toUpperCase()}
                                size="small"
                                color={getLogColor(log.type)}
                              />
                            </Box>
                          }
                          secondary={
                            <Typography
                              component="pre"
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.875rem',
                                whiteSpace: 'pre-wrap',
                                mt: 0.5
                              }}
                            >
                              {log.message}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                )}
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* معلومات إضافية */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            💡 معلومات مفيدة
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                🏢 بيانات العملاء للاختبار:
              </Typography>
              <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
{`1000 → 112223333 (محمد علي الحاشدي)
1001 → Hash2020@ (عميل تجريبي)
1002 → password123 (ون كاش)
1003 → test123 (شركة اسي)
1004 → demo123 (تجربة جديدة)
1005 → client123 (تجربة جديدة)`}
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                🔧 نقاط الفحص:
              </Typography>
              <Typography variant="body2">
                • API مباشر: /api/client/login<br/>
                • AuthContext: دالة clientLogin<br/>
                • localStorage: حفظ بيانات العميل<br/>
                • الخادم: المنفذ 8080<br/>
                • قاعدة البيانات: جدول clients
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  )
}

export default ClientLoginTest
