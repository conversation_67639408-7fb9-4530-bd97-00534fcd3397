import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  Search,
  Visibility,
  FilterList
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useAuth } from '../contexts/AuthContext'
import { useSnackbar } from 'notistack'
import ClientForm from '../components/forms/ClientForm'
import EnhancedButton from '../components/common/EnhancedButton'
import ClientDetailsDialog from '../components/dialogs/ClientDetailsDialog'

const ClientsPage = () => {
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedClient, setSelectedClient] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [clientToDelete, setClientToDelete] = useState(null)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [clientToView, setClientToView] = useState(null)

  const { api, hasPermission, user } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const queryClient = useQueryClient()

  // جلب بيانات العملاء من API مع تصفية حسب المستخدم
  const { data, isLoading, refetch } = useQuery(
    ['clients', page, pageSize, searchTerm, statusFilter, user?.id],
    () => api.get('/api/clients', {
      params: {
        page: page + 1,
        limit: pageSize,
        search: searchTerm,
        status: statusFilter,
        userId: user?.id // إضافة userId للتصفية
      }
    }).then(res => res.data),
    {
      keepPreviousData: true,
      staleTime: 30000,
      onError: (error) => {
        console.error('Error fetching clients:', error)
        enqueueSnackbar('خطأ في جلب بيانات العملاء', { variant: 'error' })
      }
    }
  )

  // حذف عميل
  const deleteMutation = useMutation(
    (clientId) => api.delete(`/api/clients/${clientId}`),
    {
      onSuccess: () => {
        enqueueSnackbar('تم حذف العميل بنجاح', { variant: 'success' })
        queryClient.invalidateQueries('clients')
        setDeleteDialogOpen(false)
        setClientToDelete(null)
      },
      onError: (error) => {
        console.error('Error deleting client:', error)
        enqueueSnackbar('خطأ في حذف العميل', { variant: 'error' })
      }
    }
  )

  // دوال التحكم في النوافذ
  const handleViewClient = (client) => {
    setClientToView(client)
    setDetailsDialogOpen(true)
  }

  const handleCloseDetailsDialog = () => {
    setDetailsDialogOpen(false)
    setClientToView(null)
  }

  const columns = [
    {
      field: 'clientCode',
      headerName: 'رمز العميل',
      width: 130,
      renderCell: (params) => (
        <Chip label={params.value} color="primary" variant="outlined" />
      )
    },
    {
      field: 'clientName',
      headerName: 'اسم العميل',
      width: 250
    },
    {
      field: 'appName',
      headerName: 'اسم التطبيق',
      width: 200
    },
    {
      field: 'ipAddress',
      headerName: 'عنوان IP',
      width: 160
    },
    {
      field: 'userId',
      headerName: 'رقم المستخدم المنشئ',
      width: 180,
      renderCell: (params) => (
        <Chip
          label={params.value || 'غير محدد'}
          color="secondary"
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'createdByUser',
      headerName: 'اسم المستخدم المنشئ',
      width: 200,
      renderCell: (params) => (
        <Chip
          label={params.value || 'غير محدد'}
          color="info"
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value === 1 ? 'مفعل' : 'محظور'}
          color={params.value === 1 ? 'success' : 'error'}
          size="small"
        />
      )
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Tooltip title="عرض التفاصيل" arrow>
            <IconButton
              size="small"
              onClick={() => handleViewClient(params.row)}
              sx={{
                color: 'primary.main',
                backgroundColor: 'primary.light',
                '&:hover': { backgroundColor: 'primary.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <span style={{ fontSize: '18px' }}>👁️</span>
            </IconButton>
          </Tooltip>
          <Tooltip title="تعديل" arrow>
            <IconButton
              size="small"
              onClick={() => handleEdit(params.row)}
              sx={{
                color: 'warning.main',
                backgroundColor: 'warning.light',
                '&:hover': { backgroundColor: 'warning.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <span style={{ fontSize: '18px' }}>✏️</span>
            </IconButton>
          </Tooltip>
          <Tooltip title="حذف" arrow>
            <IconButton
              size="small"
              onClick={() => handleDelete(params.row)}
              sx={{
                color: 'error.main',
                backgroundColor: 'error.light',
                '&:hover': { backgroundColor: 'error.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <span style={{ fontSize: '18px' }}>🗑️</span>
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ]

  const handleView = (client) => {
    setSelectedClient(client)
    setDialogOpen(true)
  }

  const handleEdit = (client) => {
    setSelectedClient(client)
    setDialogOpen(true)
  }

  const handleDelete = (client) => {
    setClientToDelete(client)
    setDeleteDialogOpen(true)
  }

  const handleAdd = () => {
    setSelectedClient(null)
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setSelectedClient(null)
  }

  const handleConfirmDelete = () => {
    if (clientToDelete) {
      deleteMutation.mutate(clientToDelete.id)
    }
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        إدارة العملاء
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="البحث في العملاء..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>حالة العميل</InputLabel>
                <Select
                  value={statusFilter}
                  label="حالة العميل"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="">الكل</MenuItem>
                  <MenuItem value="1">مفعل</MenuItem>
                  <MenuItem value="2">محظور</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={5} sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              {hasPermission('clients', 'create') && (
                <Button
                  variant="contained"
                  onClick={handleAdd}
                  color="primary"
                  sx={{
                    minWidth: '200px',
                    height: '48px',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
                    '&:hover': {
                      boxShadow: '0 6px 16px rgba(102, 126, 234, 0.4)',
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  ➕ إضافة عميل جديد
                </Button>
              )}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={data?.data || []}
              columns={columns}
              getRowId={(row) => row.id}
              pageSize={pageSize}
              onPageSizeChange={setPageSize}
              rowsPerPageOptions={[5, 10, 25, 50]}
              page={page}
              onPageChange={setPage}
              rowCount={data?.total || 0}
              paginationMode="server"
              loading={isLoading}
              disableSelectionOnClick
              localeText={{
                noRowsLabel: 'لا توجد بيانات',
                footerRowSelected: (count) => `${count} صف محدد`,
                footerTotalRows: 'إجمالي الصفوف:',
                footerPaginationRowsPerPage: 'صفوف لكل صفحة:',
              }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Dialog للإضافة/التعديل */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {selectedClient ? 'تعديل العميل' : 'إضافة عميل جديد'}
          </Typography>
          <IconButton onClick={handleCloseDialog} size="small">
            <span style={{ fontSize: '20px' }}>✕</span>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <ClientForm
            client={selectedClient}
            onSuccess={() => {
              queryClient.invalidateQueries('clients')
              handleCloseDialog()
            }}
            readOnly={!hasPermission('clients', selectedClient ? 'update' : 'create')}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog تأكيد الحذف */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">تأكيد الحذف</Typography>
          <IconButton onClick={() => setDeleteDialogOpen(false)} size="small">
            <span style={{ fontSize: '20px' }}>✕</span>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف العميل "{clientToDelete?.clientName}"؟
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>إلغاء</Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            disabled={deleteMutation.isLoading}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة عرض تفاصيل العميل */}
      <ClientDetailsDialog
        open={detailsDialogOpen}
        onClose={handleCloseDetailsDialog}
        client={clientToView}
      />
    </Box>
  )
}

export default ClientsPage
