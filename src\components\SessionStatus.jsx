import React, { useState, useEffect } from 'react';
import { Box, Typography, LinearProgress, Chip, IconButton, Tooltip } from '@mui/material';
import { AccessTime, Refresh, ExitToApp } from '@mui/icons-material';
import { useSessionTimeout } from '../hooks/useSessionTimeout';
import SessionWarningDialog from './SessionWarningDialog';

const SessionStatus = () => {
  const { getRemainingTime, resetTimer, logout, isActive, showWarning, setShowWarning } = useSessionTimeout();
  const [remainingTime, setRemainingTime] = useState(getRemainingTime());
  const [showStatus, setShowStatus] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      const remaining = getRemainingTime();
      setRemainingTime(remaining);

      // إظهار الحالة عندما يتبقى أقل من دقيقتين
      setShowStatus(remaining < 2 * 60 * 1000);
    }, 1000);

    return () => clearInterval(interval);
  }, [getRemainingTime]);

  // تحويل الوقت إلى دقائق وثواني
  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // حساب النسبة المئوية للوقت المتبقي
  const getProgressValue = () => {
    const maxTime = 5 * 60 * 1000; // 5 دقائق
    return (remainingTime / maxTime) * 100;
  };

  // تحديد لون الحالة
  const getStatusColor = () => {
    if (remainingTime > 2 * 60 * 1000) return 'success';
    if (remainingTime > 1 * 60 * 1000) return 'warning';
    return 'error';
  };

  const handleExtendSession = () => {
    setShowWarning(false);
    resetTimer();
  };

  const handleLogout = () => {
    setShowWarning(false);
    logout();
  };

  if (!showStatus || !isActive()) {
    return (
      <>
        <SessionWarningDialog
          open={showWarning}
          onExtend={handleExtendSession}
          onLogout={handleLogout}
          remainingTime={60 * 1000} // دقيقة واحدة
        />
      </>
    );
  }

  return (
    <>
      <SessionWarningDialog
        open={showWarning}
        onExtend={handleExtendSession}
        onLogout={handleLogout}
        remainingTime={60 * 1000} // دقيقة واحدة
      />
    <Box
      sx={{
        position: 'fixed',
        top: 20,
        left: 20,
        zIndex: 9999,
        backgroundColor: 'white',
        padding: 2,
        borderRadius: 2,
        boxShadow: 3,
        border: `2px solid ${getStatusColor() === 'error' ? '#f44336' : getStatusColor() === 'warning' ? '#ff9800' : '#4caf50'}`,
        minWidth: 300,
        direction: 'rtl'
      }}
    >
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
        <Box display="flex" alignItems="center" gap={1}>
          <AccessTime color={getStatusColor()} />
          <Typography variant="subtitle2" fontWeight="bold">
            حالة الجلسة
          </Typography>
        </Box>

        <Box display="flex" gap={1}>
          <Tooltip title="تجديد الجلسة">
            <IconButton size="small" onClick={resetTimer} color="primary">
              <Refresh />
            </IconButton>
          </Tooltip>

          <Tooltip title="تسجيل الخروج">
            <IconButton size="small" onClick={logout} color="error">
              <ExitToApp />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Box mb={1}>
        <Typography variant="body2" color="text.secondary">
          الوقت المتبقي: {formatTime(remainingTime)}
        </Typography>
      </Box>

      <LinearProgress
        variant="determinate"
        value={getProgressValue()}
        color={getStatusColor()}
        sx={{ height: 8, borderRadius: 4 }}
      />

      <Box mt={1} display="flex" justifyContent="space-between" alignItems="center">
        <Chip
          label={remainingTime < 1 * 60 * 1000 ? 'ستنتهي قريباً!' : 'نشطة'}
          color={getStatusColor()}
          size="small"
        />

        <Typography variant="caption" color="text.secondary">
          5 دقائق عدم نشاط
        </Typography>
      </Box>
    </Box>
    </>
  );
};

export default SessionStatus;
