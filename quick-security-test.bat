@echo off
echo 🔐 اختبار الحماية السريع
echo ========================

echo.
echo 🧪 اختبار API المحمي - المستخدمين (يجب أن يرجع 401):
curl -s -w "Status: %%{http_code}\n" "http://185.11.8.26:8080/api/users?page=1&limit=10"

echo.
echo 🧪 اختبار API المحمي - العملاء (يجب أن يرجع 401):
curl -s -w "Status: %%{http_code}\n" "http://185.11.8.26:8080/api/clients?page=1&limit=10"

echo.
echo 🧪 اختبار API العام - الصحة (يجب أن يرجع 200):
curl -s -w "Status: %%{http_code}\n" "http://185.11.8.26:8080/api/external/health"

echo.
echo 🧪 اختبار API العام - قاعدة البيانات (يجب أن يرجع 200):
curl -s -w "Status: %%{http_code}\n" "http://185.11.8.26:8080/api/external/test-db"

echo.
echo 🧪 اختبار التحقق المباشر للمطورين:
curl -X POST "http://185.11.8.26:8080/api/external/verify-direct" ^
  -H "Content-Type: application/json" ^
  -d "{\"agent_username\":\"testuser\",\"agent_password\":\"test123\",\"client_code\":\"1004\",\"client_token\":\"TEST1004\",\"device_id\":\"TEST_DEVICE_001\"}" ^
  -s -w "Status: %%{http_code}\n"

echo.
echo ✅ انتهى الاختبار
pause
