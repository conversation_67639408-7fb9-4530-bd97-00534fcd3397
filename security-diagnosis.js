// فحص تشخيصي للحماية
const http = require('http');

console.log('🔍 فحص تشخيصي للحماية');
console.log('=======================');

// فحص الخادم المحلي أولاً
function checkLocalServer() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 8080,
      path: '/api/external/health',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log('✅ الخادم المحلي يعمل على localhost:8080');
      resolve(true);
    });

    req.on('error', (err) => {
      console.log('❌ الخادم المحلي لا يعمل:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال بالخادم المحلي');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// فحص الخادم الخارجي
function checkExternalServer() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: '***********',
      port: 8080,
      path: '/api/external/health',
      method: 'GET',
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ الخادم الخارجي يعمل على ***********:8080');
        console.log('📊 Status:', res.statusCode);
        console.log('📄 Response:', data.substring(0, 200));
        resolve(true);
      });
    });

    req.on('error', (err) => {
      console.log('❌ الخادم الخارجي لا يعمل:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال بالخادم الخارجي');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// فحص API محمي
function checkProtectedAPI() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: '***********',
      port: 8080,
      path: '/api/users?page=1&limit=10',
      method: 'GET',
      timeout: 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('\n🔒 فحص API المحمي (/api/users):');
        console.log('📊 Status:', res.statusCode);
        
        if (res.statusCode === 401) {
          console.log('✅ محمي بشكل صحيح - يرجع 401');
          try {
            const jsonData = JSON.parse(data);
            if (jsonData.message && jsonData.message.includes('Token')) {
              console.log('✅ رسالة الحماية صحيحة');
            }
          } catch (e) {
            console.log('⚠️ استجابة غير JSON');
          }
        } else if (res.statusCode === 200) {
          console.log('❌ غير محمي - يعرض البيانات!');
          console.log('📄 Response:', data.substring(0, 300));
        } else {
          console.log('⚠️ Status غير متوقع:', res.statusCode);
          console.log('📄 Response:', data.substring(0, 200));
        }
        resolve(res.statusCode === 401);
      });
    });

    req.on('error', (err) => {
      console.log('❌ خطأ في فحص API المحمي:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة فحص API المحمي');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// تشغيل الفحص
async function runDiagnosis() {
  console.log('\n🔍 بدء الفحص التشخيصي...\n');
  
  const localWorks = await checkLocalServer();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const externalWorks = await checkExternalServer();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  if (externalWorks) {
    const isProtected = await checkProtectedAPI();
    
    console.log('\n📋 ملخص النتائج:');
    console.log('================');
    console.log('🏠 الخادم المحلي:', localWorks ? '✅ يعمل' : '❌ لا يعمل');
    console.log('🌐 الخادم الخارجي:', externalWorks ? '✅ يعمل' : '❌ لا يعمل');
    console.log('🔒 الحماية:', isProtected ? '✅ تعمل' : '❌ لا تعمل');
    
    if (externalWorks && isProtected) {
      console.log('\n🎉 النظام يعمل بشكل مثالي مع الحماية!');
    } else if (externalWorks && !isProtected) {
      console.log('\n⚠️ الخادم يعمل لكن الحماية لا تعمل!');
    } else {
      console.log('\n❌ يوجد مشاكل في النظام');
    }
  } else {
    console.log('\n❌ لا يمكن الوصول للخادم الخارجي');
  }
}

runDiagnosis().catch(console.error);
