<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #667eea;
            outline: none;
        }
        button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            opacity: 0.9;
        }
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .server-status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔐 اختبار تسجيل الدخول</h1>
        
        <div class="server-status">
            <strong>حالة الخادم:</strong> <span id="serverStatus">جاري الفحص...</span>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="loginName">اسم المستخدم:</label>
                <input type="text" id="loginName" name="loginName" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123456" required>
            </div>
            <button type="submit">دخول</button>
        </form>
        <div id="message"></div>
    </div>

    <script>
        // Check server status - try both ports
        async function checkServer() {
            const ports = [8080, 3000];
            let connected = false;

            for (const port of ports) {
                try {
                    const response = await fetch(`http://localhost:${port}/health`);
                    if (response.ok) {
                        document.getElementById('serverStatus').innerHTML = `<span style="color: green;">✅ متصل على المنفذ ${port}</span>`;
                        window.serverPort = port; // حفظ المنفذ للاستخدام في تسجيل الدخول
                        connected = true;
                        break;
                    }
                } catch (error) {
                    console.log(`Port ${port} not available:`, error.message);
                }
            }

            if (!connected) {
                document.getElementById('serverStatus').innerHTML = '<span style="color: red;">❌ غير متصل</span>';
            }
        }
        
        checkServer();

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const loginName = document.getElementById('loginName').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        loginName, 
                        password, 
                        deviceId: 'test-device-' + Date.now() 
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    messageDiv.innerHTML = '<div class="success">✅ ' + data.message + '<br>المستخدم: ' + data.user.username + '</div>';
                } else {
                    messageDiv.innerHTML = '<div class="error">❌ ' + (data.error || 'فشل تسجيل الدخول') + '</div>';
                }
            } catch (error) {
                messageDiv.innerHTML = '<div class="error">❌ خطأ في الاتصال: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html>
