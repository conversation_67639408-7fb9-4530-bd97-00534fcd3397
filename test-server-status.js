const http = require('http');

console.log('🔍 Testing server status...');

// Test local connection
const testLocal = () => {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:8080/api/debug/test-data', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ Local server (localhost:8080): WORKING');
        console.log('📊 Response:', data.substring(0, 100) + '...');
        resolve(true);
      });
    });
    
    req.on('error', (err) => {
      console.log('❌ Local server (localhost:8080): NOT WORKING');
      console.log('🔍 Error:', err.message);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('⏰ Local server (localhost:8080): TIMEOUT');
      req.destroy();
      resolve(false);
    });
  });
};

// Test external connection
const testExternal = () => {
  return new Promise((resolve) => {
    const req = http.get('http://***********:8080/api/debug/test-data', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ External server (***********:8080): WORKING');
        console.log('📊 Response:', data.substring(0, 100) + '...');
        resolve(true);
      });
    });
    
    req.on('error', (err) => {
      console.log('❌ External server (***********:8080): NOT WORKING');
      console.log('🔍 Error:', err.message);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('⏰ External server (***********:8080): TIMEOUT');
      req.destroy();
      resolve(false);
    });
  });
};

async function runTests() {
  console.log('🚀 Starting server status tests...\n');
  
  await testLocal();
  console.log('');
  await testExternal();
  
  console.log('\n🏁 Tests completed!');
}

runTests();
