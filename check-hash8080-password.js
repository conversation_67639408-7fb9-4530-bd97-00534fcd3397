// التحقق من كلمة مرور المستخدم hash8080
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "postgresql://postgres:yemen123@localhost:5432/yemclient_db"
    }
  }
});

async function checkHash8080Password() {
  console.log('🔍 التحقق من كلمة مرور المستخدم hash8080');
  console.log('=' .repeat(50));
  
  try {
    // البحث عن المستخدم
    const user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        password: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    if (!user) {
      console.log('❌ المستخدم hash8080 غير موجود في قاعدة البيانات');
      
      // عرض جميع المستخدمين
      const allUsers = await prisma.user.findMany({
        select: { loginName: true, username: true, isActive: true }
      });
      
      console.log('\n📋 المستخدمون الموجودون:');
      allUsers.forEach(u => {
        console.log(`   - ${u.loginName} (${u.username}) - ${u.isActive ? 'نشط' : 'غير نشط'}`);
      });
      return;
    }
    
    console.log('✅ تم العثور على المستخدم:');
    console.log(`   الاسم: ${user.username}`);
    console.log(`   اسم الدخول: ${user.loginName}`);
    console.log(`   الحالة: ${user.isActive ? 'نشط' : 'غير نشط'}`);
    console.log(`   تاريخ الإنشاء: ${user.createdAt.toLocaleString('ar-EG')}`);
    console.log(`   تاريخ التحديث: ${user.updatedAt.toLocaleString('ar-EG')}`);
    
    // اختبار كلمات المرور المحتملة
    const possiblePasswords = [
      'Hash8080',
      'hash8080', 
      'HASH8080',
      'Hash@8080',
      'hash@8080',
      '8080',
      'yemen123',
      'admin123456'
    ];
    
    console.log('\n🧪 اختبار كلمات المرور المحتملة:');
    console.log('-' .repeat(40));
    
    let correctPassword = null;
    
    for (const testPassword of possiblePasswords) {
      try {
        const isMatch = await bcrypt.compare(testPassword, user.password);
        const status = isMatch ? '✅ صحيحة' : '❌ خطأ';
        console.log(`   ${testPassword.padEnd(15)} → ${status}`);
        
        if (isMatch) {
          correctPassword = testPassword;
        }
      } catch (error) {
        console.log(`   ${testPassword.padEnd(15)} → ❌ خطأ في الاختبار`);
      }
    }
    
    console.log('\n📋 النتيجة النهائية:');
    console.log('=' .repeat(30));
    
    if (correctPassword) {
      console.log(`✅ كلمة المرور الصحيحة: ${correctPassword}`);
      console.log('\n🎯 بيانات تسجيل الدخول:');
      console.log(`   اسم المستخدم: ${user.loginName}`);
      console.log(`   كلمة المرور: ${correctPassword}`);
      console.log(`   الرابط: http://127.0.0.1:8080/`);
    } else {
      console.log('❌ لم يتم العثور على كلمة المرور الصحيحة');
      console.log('💡 قد تحتاج لإعادة تعيين كلمة المرور');
      
      console.log('\n🔐 معلومات كلمة المرور المشفرة:');
      console.log(`   Hash: ${user.password.substring(0, 30)}...`);
      console.log(`   طول الـ Hash: ${user.password.length} حرف`);
      console.log(`   نوع التشفير: ${user.password.startsWith('$2b$') ? 'bcrypt' : 'غير معروف'}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في التحقق من كلمة المرور:', error);
    
    if (error.code === 'P1001') {
      console.error('💡 لا يمكن الاتصال بقاعدة البيانات');
      console.error('   تأكد من تشغيل PostgreSQL');
    } else if (error.code === 'P2021') {
      console.error('💡 جدول المستخدمين غير موجود');
      console.error('   قم بتشغيل: npx prisma migrate dev');
    }
  } finally {
    await prisma.$disconnect();
  }
}

checkHash8080Password()
  .then(() => {
    console.log('\n🏁 انتهى الفحص');
  })
  .catch((error) => {
    console.error('\n💥 فشل الفحص:', error.message);
  });
