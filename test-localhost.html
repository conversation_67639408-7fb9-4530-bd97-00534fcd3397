<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال المحلي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            opacity: 0.9;
        }
        .url-list {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .url-item {
            margin: 5px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 اختبار الاتصال المحلي</h1>
        
        <div class="test-section">
            <div class="test-title">1. اختبار الروابط المختلفة</div>
            <button onclick="testAllUrls()">اختبار جميع الروابط</button>
            <div id="urlResults"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. اختبار تسجيل الدخول</div>
            <div class="info">
                <strong>بيانات تسجيل الدخول:</strong><br>
                • اسم المستخدم: hash8080<br>
                • كلمة المرور: Hash8080 (بحرف H كبير)
            </div>
            <button onclick="testLogin()">اختبار تسجيل الدخول</button>
            <div id="loginResults"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. الحلول المقترحة</div>
            <div class="info">
                <strong>إذا كان localhost لا يعمل:</strong><br>
                1. استخدم الرابط الخارجي: <a href="http://***********:8080" target="_blank">http://***********:8080</a><br>
                2. جرب 127.0.0.1 بدلاً من localhost<br>
                3. تحقق من جدار الحماية<br>
                4. أعد تشغيل الخادم
            </div>
        </div>
    </div>

    <script>
        const urls = [
            'http://localhost:8080',
            'http://127.0.0.1:8080',
            'http://***********:8080',
            'http://localhost:8080/health',
            'http://127.0.0.1:8080/health',
            'http://***********:8080/health'
        ];

        async function testAllUrls() {
            const resultDiv = document.getElementById('urlResults');
            resultDiv.innerHTML = '<div class="info">🔄 جاري اختبار الروابط...</div>';
            
            let results = [];
            
            for (const url of urls) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(url, {
                        method: 'GET',
                        mode: 'cors',
                        cache: 'no-cache'
                    });
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    
                    if (response.ok) {
                        results.push(`✅ ${url} - يعمل (${response.status}) - ${responseTime}ms`);
                    } else {
                        results.push(`⚠️ ${url} - خطأ (${response.status}) - ${responseTime}ms`);
                    }
                } catch (error) {
                    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                        results.push(`❌ ${url} - لا يمكن الوصول (انقطاع الاتصال)`);
                    } else if (error.message.includes('CORS')) {
                        results.push(`🔒 ${url} - مشكلة CORS`);
                    } else {
                        results.push(`❌ ${url} - خطأ: ${error.message}`);
                    }
                }
            }
            
            const workingUrls = results.filter(r => r.includes('✅'));
            const summary = workingUrls.length > 0 ? 
                `<div class="success">✅ ${workingUrls.length} من ${urls.length} روابط تعمل</div>` :
                `<div class="error">❌ لا توجد روابط تعمل</div>`;
            
            resultDiv.innerHTML = summary + '<div class="url-list">' + results.map(r => `<div class="url-item">${r}</div>`).join('') + '</div>';
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResults');
            resultDiv.innerHTML = '<div class="info">🔄 جاري اختبار تسجيل الدخول...</div>';
            
            const loginData = {
                loginName: 'hash8080',
                password: 'Hash8080',
                deviceId: 'test-device-' + Date.now()
            };
            
            const testUrls = [
                'http://localhost:8080/api/auth/login',
                'http://127.0.0.1:8080/api/auth/login',
                'http://***********:8080/api/auth/login'
            ];
            
            let results = [];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(loginData)
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        results.push(`✅ ${url} - تسجيل دخول ناجح`);
                    } else {
                        results.push(`❌ ${url} - فشل: ${data.error || 'خطأ غير معروف'}`);
                    }
                } catch (error) {
                    if (error.message.includes('Failed to fetch')) {
                        results.push(`❌ ${url} - لا يمكن الوصول`);
                    } else {
                        results.push(`❌ ${url} - خطأ: ${error.message}`);
                    }
                }
            }
            
            const successfulLogins = results.filter(r => r.includes('✅'));
            const summary = successfulLogins.length > 0 ? 
                `<div class="success">✅ تسجيل الدخول نجح على ${successfulLogins.length} رابط</div>` :
                `<div class="error">❌ فشل تسجيل الدخول على جميع الروابط</div>`;
            
            resultDiv.innerHTML = summary + '<div class="url-list">' + results.map(r => `<div class="url-item">${r}</div>`).join('') + '</div>';
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            testAllUrls();
        };
    </script>
</body>
</html>
