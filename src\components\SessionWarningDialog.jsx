import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  LinearProgress,
  Alert
} from '@mui/material';
import { Warning, AccessTime } from '@mui/icons-material';

const SessionWarningDialog = ({ open, onExtend, onLogout, remainingTime }) => {
  const [timeLeft, setTimeLeft] = useState(remainingTime);

  useEffect(() => {
    if (open && remainingTime > 0) {
      setTimeLeft(remainingTime);
      
      const interval = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1000;
          if (newTime <= 0) {
            clearInterval(interval);
            onLogout();
            return 0;
          }
          return newTime;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [open, remainingTime, onLogout]);

  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getProgressValue = () => {
    const maxTime = 60 * 1000; // دقيقة واحدة
    return (timeLeft / maxTime) * 100;
  };

  return (
    <Dialog
      open={open}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          direction: 'rtl',
          textAlign: 'center'
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
          <Warning color="warning" fontSize="large" />
          <Typography variant="h6" component="span">
            تحذير انتهاء الجلسة
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            ستنتهي جلستك قريباً بسبب عدم النشاط
          </Typography>
        </Alert>

        <Box sx={{ mb: 3 }}>
          <Box display="flex" alignItems="center" justifyContent="center" gap={1} mb={2}>
            <AccessTime color="error" />
            <Typography variant="h5" color="error" fontWeight="bold">
              {formatTime(timeLeft)}
            </Typography>
          </Box>

          <LinearProgress
            variant="determinate"
            value={getProgressValue()}
            color="error"
            sx={{ height: 10, borderRadius: 5 }}
          />
        </Box>

        <Typography variant="body2" color="text.secondary">
          اختر "متابعة الجلسة" للبقاء مسجلاً، أو "تسجيل الخروج" للخروج الآن
        </Typography>
      </DialogContent>

      <DialogActions sx={{ justifyContent: 'center', gap: 2, pb: 3 }}>
        <Button
          onClick={onExtend}
          variant="contained"
          color="primary"
          size="large"
          sx={{ minWidth: 150 }}
        >
          متابعة الجلسة
        </Button>
        
        <Button
          onClick={onLogout}
          variant="outlined"
          color="error"
          size="large"
          sx={{ minWidth: 150 }}
        >
          تسجيل الخروج
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionWarningDialog;
