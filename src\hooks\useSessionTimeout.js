import { useEffect, useRef, useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';

const SESSION_TIMEOUT = 5 * 60 * 1000; // 5 دقائق بالميلي ثانية
const WARNING_TIME = 1 * 60 * 1000; // تحذير قبل دقيقة واحدة

export const useSessionTimeout = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const timeoutRef = useRef(null);
  const warningRef = useRef(null);
  const lastActivityRef = useRef(Date.now());
  const [showWarning, setShowWarning] = useState(false);

  // التحقق من نوع المستخدم - إذا كان عميل، لا نطبق انتهاء الجلسة
  const isClientSession = () => {
    const clientData = localStorage.getItem('clientData');
    return !!clientData;
  };

  // تسجيل الخروج
  const logout = useCallback(() => {
    // التحقق من وجود جلسة فعلية قبل عرض الرسالة
    const hasActiveSession = localStorage.getItem('token') || localStorage.getItem('user');

    // مسح التوكن من localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // مسح المؤقتات
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (warningRef.current) {
      clearTimeout(warningRef.current);
    }

    // عرض رسالة فقط إذا كانت هناك جلسة فعلية
    if (hasActiveSession) {
      enqueueSnackbar('تم انتهاء الجلسة بسبب عدم النشاط. يرجى تسجيل الدخول مرة أخرى.', {
        variant: 'error'
      });
    }

    // التوجه لصفحة تسجيل الدخول
    navigate('/login', { replace: true });
  }, [navigate, enqueueSnackbar]);

  // عرض تحذير قبل انتهاء الجلسة
  const showWarningDialog = useCallback(() => {
    setShowWarning(true);
    enqueueSnackbar('ستنتهي جلستك خلال دقيقة واحدة بسبب عدم النشاط. قم بأي نشاط للاستمرار.', {
      variant: 'warning'
    });
  }, [enqueueSnackbar]);

  // إعادة تعيين المؤقت
  const resetTimer = useCallback(() => {
    // إذا كان المستخدم عميل، لا نطبق انتهاء الجلسة
    if (isClientSession()) {
      return;
    }

    lastActivityRef.current = Date.now();
    setShowWarning(false);

    // مسح المؤقتات السابقة
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (warningRef.current) {
      clearTimeout(warningRef.current);
    }

    // تعيين مؤقت التحذير
    warningRef.current = setTimeout(() => {
      showWarningDialog();
    }, SESSION_TIMEOUT - WARNING_TIME);

    // تعيين مؤقت تسجيل الخروج
    timeoutRef.current = setTimeout(() => {
      logout();
    }, SESSION_TIMEOUT);
  }, [logout, showWarningDialog]);

  // التحقق من صحة التوكن
  const checkTokenValidity = useCallback(() => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    // إذا لم يكن هناك توكن أو مستخدم، فهذا يعني أنه لم يسجل دخول أصلاً
    if (!token || !user) {
      return false;
    }
    return true;
  }, []);

  // معالج الأحداث للنشاط
  const handleActivity = useCallback(() => {
    if (checkTokenValidity()) {
      resetTimer();
    }
  }, [resetTimer, checkTokenValidity]);

  useEffect(() => {
    // إذا كان المستخدم عميل، لا نطبق انتهاء الجلسة
    if (isClientSession()) {
      return;
    }

    // التحقق من وجود التوكن عند بدء التشغيل
    if (!checkTokenValidity()) {
      return;
    }

    // التحقق من وجود مستخدم مسجل فعلياً
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    if (!token || !user) {
      return;
    }

    // قائمة الأحداث التي تدل على النشاط
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
      'keydown'
    ];

    // إضافة مستمعي الأحداث
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // بدء المؤقت
    resetTimer();

    // تنظيف عند إلغاء التحميل
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (warningRef.current) {
        clearTimeout(warningRef.current);
      }
    };
  }, [handleActivity, resetTimer, checkTokenValidity]);

  // إرجاع دوال مفيدة
  return {
    resetTimer,
    logout,
    showWarning,
    setShowWarning,
    getRemainingTime: () => {
      const elapsed = Date.now() - lastActivityRef.current;
      return Math.max(0, SESSION_TIMEOUT - elapsed);
    },
    isActive: () => {
      const elapsed = Date.now() - lastActivityRef.current;
      return elapsed < SESSION_TIMEOUT;
    },
    getWarningTime: () => {
      const elapsed = Date.now() - lastActivityRef.current;
      const timeToWarning = SESSION_TIMEOUT - WARNING_TIME - elapsed;
      return Math.max(0, timeToWarning);
    }
  };
};
