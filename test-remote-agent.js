#!/usr/bin/env node

/**
 * اختبار شامل لخدمة Remote Agent
 * يتحقق من جميع APIs والاتصالات
 */

const axios = require('axios');
const { execSync } = require('child_process');

// إعدادات الاختبار
const CONFIG = {
  SERVER_URL: 'http://localhost:8081',
  EXTERNAL_URL: 'http://***********:8081',
  CLIENT_URL: 'http://localhost:5173',
  TEST_DATA: {
    username: 'testuser',
    password: 'test123',
    clientCode: 1004,
    clientToken: 'TEST1004',
    deviceId: 'DEV001'
  }
};

// ألوان للطباعة
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

// فحص متطلبات النظام
async function checkSystemRequirements() {
  log('\n🔍 فحص متطلبات النظام...', 'bold');
  
  try {
    // فحص Node.js
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    logSuccess(`Node.js: ${nodeVersion}`);
  } catch (error) {
    logError('Node.js غير مثبت');
    return false;
  }

  try {
    // فحص npm
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    logSuccess(`npm: ${npmVersion}`);
  } catch (error) {
    logError('npm غير متاح');
    return false;
  }

  try {
    // فحص PostgreSQL
    const pgVersion = execSync('psql --version', { encoding: 'utf8' }).trim();
    logSuccess(`PostgreSQL: ${pgVersion}`);
  } catch (error) {
    logWarning('PostgreSQL غير متاح في PATH');
  }

  return true;
}

// فحص حالة الخادم
async function checkServerHealth() {
  log('\n🏥 فحص حالة الخادم...', 'bold');
  
  const endpoints = [
    { name: 'الخادم المحلي', url: `${CONFIG.SERVER_URL}/api/external/health` },
    { name: 'الخادم الخارجي', url: `${CONFIG.EXTERNAL_URL}/api/external/health` }
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(endpoint.url, { timeout: 5000 });
      if (response.status === 200) {
        logSuccess(`${endpoint.name}: متاح`);
      } else {
        logWarning(`${endpoint.name}: استجابة غير متوقعة (${response.status})`);
      }
    } catch (error) {
      logError(`${endpoint.name}: غير متاح - ${error.message}`);
    }
  }
}

// اختبار API الوكلاء
async function testAgentAPI() {
  log('\n🧪 اختبار API الوكلاء...', 'bold');
  
  const testCases = [
    {
      name: 'النموذج الجديد',
      data: CONFIG.TEST_DATA
    },
    {
      name: 'النموذج القديم',
      data: {
        agent_login_name: CONFIG.TEST_DATA.username,
        agent_login_password: CONFIG.TEST_DATA.password,
        client_code: CONFIG.TEST_DATA.clientCode,
        client_token: CONFIG.TEST_DATA.clientToken
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      logInfo(`اختبار ${testCase.name}...`);
      
      const response = await axios.post(
        `${CONFIG.SERVER_URL}/api/external/verify-direct`,
        testCase.data,
        {
          headers: { 'Content-Type': 'application/json' },
          timeout: 10000
        }
      );

      if (response.status === 200) {
        logSuccess(`${testCase.name}: نجح الاختبار`);
        logInfo(`الاستجابة: ${JSON.stringify(response.data, null, 2)}`);
      } else {
        logWarning(`${testCase.name}: استجابة غير متوقعة (${response.status})`);
      }
    } catch (error) {
      if (error.response) {
        logError(`${testCase.name}: فشل - ${error.response.status} ${error.response.statusText}`);
        logInfo(`تفاصيل الخطأ: ${JSON.stringify(error.response.data, null, 2)}`);
      } else {
        logError(`${testCase.name}: فشل - ${error.message}`);
      }
    }
  }
}

// فحص واجهة العميل
async function checkClientInterface() {
  log('\n🌐 فحص واجهة العميل...', 'bold');
  
  try {
    const response = await axios.get(CONFIG.CLIENT_URL, { timeout: 5000 });
    if (response.status === 200) {
      logSuccess('واجهة العميل: متاحة');
    } else {
      logWarning(`واجهة العميل: استجابة غير متوقعة (${response.status})`);
    }
  } catch (error) {
    logError(`واجهة العميل: غير متاحة - ${error.message}`);
    logInfo('تأكد من تشغيل العميل باستخدام: cd client && npm run dev');
  }
}

// فحص قاعدة البيانات
async function checkDatabase() {
  log('\n🗄️ فحص قاعدة البيانات...', 'bold');
  
  try {
    // محاولة الاتصال بقاعدة البيانات عبر API
    const response = await axios.get(`${CONFIG.SERVER_URL}/api/external/health`, { timeout: 5000 });
    if (response.data && response.data.database) {
      logSuccess('قاعدة البيانات: متصلة');
    } else {
      logWarning('قاعدة البيانات: حالة غير واضحة');
    }
  } catch (error) {
    logError('قاعدة البيانات: فشل في الفحص');
  }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  log('🚀 بدء اختبار خدمة Remote Agent', 'bold');
  log('========================================', 'blue');
  
  const systemOk = await checkSystemRequirements();
  if (!systemOk) {
    logError('فشل في فحص متطلبات النظام');
    process.exit(1);
  }

  await checkServerHealth();
  await checkDatabase();
  await testAgentAPI();
  await checkClientInterface();
  
  log('\n========================================', 'blue');
  log('🎉 اكتمل اختبار النظام', 'bold');
  
  // ملخص النتائج
  log('\n📋 ملخص النتائج:', 'bold');
  log('- تحقق من الرسائل أعلاه للتأكد من عمل جميع المكونات');
  log('- إذا ظهرت أخطاء، راجع دليل استكشاف الأخطاء');
  log('- للمساعدة، راجع ملف دليل-تشغيل-Remote-Agent.md');
}

// تشغيل الاختبارات
if (require.main === module) {
  runAllTests().catch(error => {
    logError(`خطأ في تشغيل الاختبارات: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  checkSystemRequirements,
  checkServerHealth,
  testAgentAPI,
  checkClientInterface,
  checkDatabase
};
