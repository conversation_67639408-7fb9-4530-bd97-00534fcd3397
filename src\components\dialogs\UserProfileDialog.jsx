import React, { useState } from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  Box,
  Chip,
  Divider,
  Card,
  CardContent,
  IconButton,
  TextField,
  Alert,
  CircularProgress
} from '@mui/material'
import {
  Person as PersonIcon,
  VpnKey as VpnKeyIcon,
  Security as SecurityIcon,
  CalendarToday as CalendarIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Check as CheckIcon
} from '@mui/icons-material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useMutation } from 'react-query'
import { useAuth } from '../../contexts/AuthContext'
import { useSnackbar } from 'notistack'

const passwordSchema = yup.object({
  currentPassword: yup.string().required('كلمة المرور الحالية مطلوبة'),
  newPassword: yup.string()
    .required('كلمة المرور الجديدة مطلوبة')
    .min(8, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    .matches(/[A-Z]/, 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل')
    .matches(/[a-z]/, 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل')
    .matches(/\d/, 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل')
    .matches(/[!@#$%^&*]/, 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل (!@#$%^&*)'),
  confirmPassword: yup.string()
    .required('تأكيد كلمة المرور مطلوب')
    .oneOf([yup.ref('newPassword')], 'كلمة المرور غير متطابقة')
})

const UserProfileDialog = ({ open, onClose }) => {
  const { user, api } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const [editingPassword, setEditingPassword] = useState(false)
  const [editingDevice, setEditingDevice] = useState(false)
  const [device1Value, setDevice1Value] = useState('')

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(passwordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  })

  // تحديث كلمة المرور
  const passwordMutation = useMutation(
    (data) => api.put(`/api/users/${user.id}/password`, data),
    {
      onSuccess: () => {
        enqueueSnackbar('تم تحديث كلمة المرور بنجاح', { variant: 'success' })
        setEditingPassword(false)
        reset()
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.error || 'فشل في تحديث كلمة المرور',
          { variant: 'error' }
        )
      }
    }
  )

  const onSubmitPassword = (data) => {
    passwordMutation.mutate(data)
  }

  const handleCancelEdit = () => {
    setEditingPassword(false)
    reset()
  }

  // تحديث معرف الجهاز2
  const deviceMutation = useMutation(
    (data) => api.put(`/api/users/${user.id}/device`, data),
    {
      onSuccess: () => {
        enqueueSnackbar('تم تحديث معرف الجهاز2 بنجاح', { variant: 'success' })
        setEditingDevice(false)
        // إعادة تحميل بيانات المستخدم
        window.location.reload()
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.error || 'فشل في تحديث معرف الجهاز2',
          { variant: 'error' }
        )
      }
    }
  )

  const handleSaveDevice = () => {
    deviceMutation.mutate({ device1: device1Value })
  }

  const handleCancelDeviceEdit = () => {
    setEditingDevice(false)
    setDevice1Value(user.device1 || '')
  }

  // تحديث قيمة معرف الجهاز2 عند فتح النافذة
  React.useEffect(() => {
    if (user) {
      setDevice1Value(user.device1 || '')
    }
  }, [user])

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!user) return null

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontSize: '1.5rem',
        fontWeight: 'bold'
      }}>
        <span>👤 الملف الشخصي</span>
        <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
          <span style={{ fontSize: '20px' }}>✕</span>
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* معلومات المستخدم الأساسية */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PersonIcon /> معلومات المستخدم
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>رقم المستخدم:</strong>
                      </Typography>
                      <Chip
                        label={user.id}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>النوع:</strong>
                      </Typography>
                      <Chip
                        label={user.permissions?.isAdmin ? 'مدير النظام' : 'مستخدم عادي'}
                        color={user.permissions?.isAdmin ? 'primary' : 'default'}
                        size="small"
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '100px' }}>
                        <strong>اسم المستخدم:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{user.username}</Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '100px' }}>
                        <strong>اسم الدخول:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{user.loginName}</Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '100px' }}>
                        <strong>معرف جهاز2:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.85rem', fontFamily: 'monospace' }}>
                        {user.device1 || 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات الأمان */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <VpnKeyIcon /> كلمة المرور
                  </Box>
                  {!editingPassword && (
                    <IconButton
                      size="small"
                      onClick={() => setEditingPassword(true)}
                      sx={{ color: '#2c3e50' }}
                    >
                      <EditIcon />
                    </IconButton>
                  )}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                {!editingPassword ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                      <strong>كلمة المرور:</strong>
                    </Typography>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {'•'.repeat(12)}
                    </Typography>
                  </Box>
                ) : (
                  <Box component="form" onSubmit={handleSubmit(onSubmitPassword)}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Alert severity="info" sx={{ mb: 2 }}>
                          لتغيير كلمة المرور، يرجى إدخال كلمة المرور الحالية وكلمة المرور الجديدة
                        </Alert>
                      </Grid>

                      <Grid item xs={12}>
                        <Controller
                          name="currentPassword"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              fullWidth
                              label="كلمة المرور الحالية"
                              type="password"
                              error={!!errors.currentPassword}
                              helperText={errors.currentPassword?.message}
                              size="small"
                            />
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Controller
                          name="newPassword"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              fullWidth
                              label="كلمة المرور الجديدة"
                              type="password"
                              error={!!errors.newPassword}
                              helperText={errors.newPassword?.message}
                              size="small"
                            />
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Controller
                          name="confirmPassword"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              fullWidth
                              label="تأكيد كلمة المرور"
                              type="password"
                              error={!!errors.confirmPassword}
                              helperText={errors.confirmPassword?.message}
                              size="small"
                            />
                          )}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                          <Button
                            type="submit"
                            variant="contained"
                            startIcon={<SaveIcon />}
                            disabled={passwordMutation.isLoading}
                            size="small"
                          >
                            حفظ
                          </Button>
                          <Button
                            variant="outlined"
                            startIcon={<CancelIcon />}
                            onClick={handleCancelEdit}
                            size="small"
                          >
                            إلغاء
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات الأجهزة */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SecurityIcon /> معلومات الأجهزة
                  </Box>
                  {!editingDevice && (
                    <IconButton
                      size="small"
                      onClick={() => setEditingDevice(true)}
                      sx={{ color: '#2c3e50' }}
                      title="تعديل معرف جهاز2"
                    >
                      <EditIcon />
                    </IconButton>
                  )}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>الجهاز الأساسي:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
                        {user.deviceId || 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>معرف جهاز2:</strong>
                      </Typography>
                      {!editingDevice ? (
                        <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
                          {user.device1 || 'غير محدد'}
                        </Typography>
                      ) : (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                          <TextField
                            value={device1Value}
                            onChange={(e) => setDevice1Value(e.target.value)}
                            placeholder="أدخل معرف الجهاز الثاني"
                            size="small"
                            sx={{ flex: 1 }}
                            inputProps={{
                              style: { fontFamily: 'monospace', fontSize: '0.8rem' }
                            }}
                          />
                          <IconButton
                            size="small"
                            onClick={handleSaveDevice}
                            disabled={deviceMutation.isLoading}
                            sx={{ color: 'success.main' }}
                            title="حفظ"
                          >
                            {deviceMutation.isLoading ? (
                              <CircularProgress size={16} />
                            ) : (
                              <CheckIcon />
                            )}
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={handleCancelDeviceEdit}
                            sx={{ color: 'error.main' }}
                            title="إلغاء"
                          >
                            <CancelIcon />
                          </IconButton>
                        </Box>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات التواريخ */}
          <Grid item xs={12}>
            <Card sx={{ background: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#8e44ad', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CalendarIcon /> معلومات التواريخ
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>تاريخ الإنشاء:</strong>
                      </Typography>
                      <Typography variant="body2">
                        {user.createdAt ? formatDate(user.createdAt) : 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>آخر تحديث:</strong>
                      </Typography>
                      <Typography variant="body2">
                        {user.updatedAt ? formatDate(user.updatedAt) : 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3, justifyContent: 'center' }}>
        <Button
          onClick={onClose}
          variant="contained"
          sx={{
            minWidth: '120px',
            borderRadius: '25px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
            }
          }}
        >
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default UserProfileDialog
