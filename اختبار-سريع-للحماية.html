<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 اختبار عاجل للحماية</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        .alert {
            background: #e74c3c;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 2px solid #c0392b;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
            100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s;
        }
        .test-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
        }
        .success { background: #27ae60; }
        .error { background: #e74c3c; }
        .warning { background: #f39c12; }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .big-text {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="alert">
            <h1>🚨 تحذير أمني عاجل!</h1>
            <p>تم اكتشاف أن API المستخدمين غير محمي ويعرض بيانات حساسة!</p>
            <p><strong>العنوان المكشوف:</strong> http://***********:8080/api/users</p>
        </div>

        <div class="big-text">
            🔍 اختبار سريع للحماية
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="testProtection()">
                🧪 اختبار الحماية الآن
            </button>
            <button class="test-button" onclick="location.reload()">
                🔄 إعادة الاختبار
            </button>
        </div>

        <div id="results"></div>

        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin-top: 30px;">
            <h3>📋 ما يجب أن يحدث:</h3>
            <ul>
                <li><strong>✅ إذا كان محمي:</strong> يرجع خطأ 401 "غير مصرح لك بالوصول"</li>
                <li><strong>❌ إذا كان غير محمي:</strong> يرجع قائمة بجميع المستخدمين (خطر أمني!)</li>
            </ul>
            
            <h3>🛠️ إذا كان غير محمي:</h3>
            <ol>
                <li>أوقف جميع خوادم Node.js فوراً</li>
                <li>شغل الخادم المحمي: <code>cd server && node working-server.js</code></li>
                <li>أعد الاختبار</li>
            </ol>
        </div>
    </div>

    <script>
        async function testProtection() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result warning"><div class="loading"></div> جاري اختبار الحماية...</div>';

            try {
                const response = await fetch('http://***********:8080/api/users', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.status === 401) {
                    // محمي - هذا ما نريده
                    resultsDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ ممتاز! النظام محمي</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>الرسالة:</strong> ${data.message || 'غير مصرح بالوصول'}</p>
                            <p>🎉 تم إصلاح المشكلة الأمنية بنجاح!</p>
                        </div>
                    `;
                } else if (response.status === 200 && data.data) {
                    // غير محمي - خطر أمني!
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ خطر أمني! النظام غير محمي</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>البيانات المكشوفة:</strong> ${data.data.length} مستخدم</p>
                            <p><strong>🚨 يجب إصلاح هذا فوراً!</strong></p>
                            <details>
                                <summary>عرض البيانات المكشوفة (انقر للتوسيع)</summary>
                                <pre style="background: rgba(0,0,0,0.5); padding: 10px; border-radius: 5px; overflow: auto; max-height: 300px;">${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    // حالة غير متوقعة
                    resultsDiv.innerHTML = `
                        <div class="result warning">
                            <h3>⚠️ نتيجة غير متوقعة</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>البيانات:</strong></p>
                            <pre style="background: rgba(0,0,0,0.5); padding: 10px; border-radius: 5px; overflow: auto; max-height: 200px;">${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ خطأ في الاتصال</h3>
                        <p><strong>الخطأ:</strong> ${error.message}</p>
                        <p>قد يكون الخادم متوقف أو هناك مشكلة في الشبكة</p>
                    </div>
                `;
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            setTimeout(testProtection, 1000);
        };
    </script>
</body>
</html>
