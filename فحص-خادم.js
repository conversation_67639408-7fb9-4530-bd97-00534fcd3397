// فحص سريع لحالة الخادم
const http = require('http');

console.log('🔍 فحص حالة الخادم...\n');

// فحص الخادم المحلي
function checkServer() {
  const options = {
    hostname: 'localhost',
    port: 8080,
    path: '/api/external/verify-direct',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 5000
  };

  const testData = JSON.stringify({
    agent_login_name: 'testuser',
    agent_login_password: 'test123',
    client_code: '1000',
    client_token: 'ABC12345'
  });

  console.log('📡 اختبار الخادم على localhost:8080...');
  
  const req = http.request(options, (res) => {
    console.log(`✅ الخادم يستجيب! رمز الحالة: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('📋 الرد من الخادم:');
      console.log(data);
      
      try {
        const json = JSON.parse(data);
        console.log('\n✅ JSON صحيح - الخادم يعمل بشكل طبيعي!');
      } catch (e) {
        console.log('\n❌ الرد ليس JSON صحيح - قد يكون هناك خطأ في الخادم');
      }
    });
  });

  req.on('error', (error) => {
    console.log(`❌ خطأ في الاتصال: ${error.message}`);
    console.log('\n🔧 الحلول:');
    console.log('1. تشغيل الخادم: انقر مرتين على تشغيل-الخادم.bat');
    console.log('2. أو تشغيل يدوي: cd server && node working-server.js');
    console.log('3. تحقق من المنفذ 8080 متاح');
  });

  req.on('timeout', () => {
    console.log('⏰ انتهت مهلة الطلب - الخادم لا يستجيب');
    req.destroy();
  });

  req.write(testData);
  req.end();
}

checkServer();
