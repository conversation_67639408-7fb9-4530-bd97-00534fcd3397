package models

import (
	"time"
)

type DataRecord struct {
	ID              uint      `gorm:"primaryKey"`
	ClientID        uint      // Foreign key to Client
	AgentID         uint      // Foreign key to Agent
	ClientCode      string    `gorm:"size:20"`
	AgentReference  string    `gorm:"size:50"`
	OperationDate   time.Time
	OperationStatus int       // 0=pending, 1=completed, 2=failed
	Amount          float64
	CreatedAt       time.Time
	UpdatedAt       time.Time
}
