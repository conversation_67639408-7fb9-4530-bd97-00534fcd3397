@echo off
echo 🚀 تشغيل خادم نظام إدارة العملاء...
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Node.js
node --version

echo.
echo 🔧 بدء تشغيل الخادم البسيط...
echo 📁 المجلد: %CD%
echo 🌐 الرابط: http://localhost:8080
echo.

REM تشغيل الخادم
node start-simple.js

pause
