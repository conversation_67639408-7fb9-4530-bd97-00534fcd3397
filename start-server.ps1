Write-Host "🚀 تشغيل خادم نظام إدارة العملاء..." -ForegroundColor Green
Write-Host ""

# التحقق من وجود Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ تم العثور على Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير مثبت أو غير موجود في PATH" -ForegroundColor Red
    Write-Host "يرجى تثبيت Node.js من https://nodejs.org" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "🔧 بدء تشغيل الخادم البسيط..." -ForegroundColor Cyan
Write-Host "📁 المجلد: $(Get-Location)" -ForegroundColor Gray
Write-Host "🌐 الرابط: http://localhost:8080" -ForegroundColor Yellow
Write-Host ""

# تشغيل الخادم
try {
    node start-simple.js
} catch {
    Write-Host "❌ فشل في تشغيل الخادم: $_" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
}
