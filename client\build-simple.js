// بناء التطبيق بطريقة بسيطة
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔨 بدء عملية البناء...');

try {
    // التحقق من وجود node_modules
    if (!fs.existsSync('node_modules')) {
        console.log('📦 تثبيت المتطلبات...');
        execSync('npm install', { stdio: 'inherit' });
    }
    
    // بناء التطبيق
    console.log('🏗️ بناء التطبيق...');
    execSync('npx vite build', { stdio: 'inherit' });
    
    console.log('✅ تم البناء بنجاح!');
    console.log('📁 الملفات المبنية في: dist/');
    
} catch (error) {
    console.error('❌ خطأ في البناء:', error.message);
    process.exit(1);
}
