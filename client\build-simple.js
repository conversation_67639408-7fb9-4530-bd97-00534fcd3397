// بناء التطبيق بطريقة بسيطة
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔨 بدء عملية البناء...');

function runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
        console.log(`🔧 تشغيل: ${command} ${args.join(' ')}`);

        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true,
            ...options
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Command failed with code ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

async function build() {
    try {
        // التحقق من وجود node_modules
        if (!fs.existsSync('node_modules')) {
            console.log('📦 تثبيت المتطلبات...');
            await runCommand('npm', ['install']);
        }

        // بناء التطبيق
        console.log('🏗️ بناء التطبيق...');
        await runCommand('npx', ['vite', 'build']);

        console.log('✅ تم البناء بنجاح!');
        console.log('📁 الملفات المبنية في: dist/');

        // التحقق من الملفات المبنية
        if (fs.existsSync('dist/index.html')) {
            console.log('✅ تم إنشاء index.html');
        }

        const assetsDir = 'dist/assets';
        if (fs.existsSync(assetsDir)) {
            const files = fs.readdirSync(assetsDir);
            console.log(`✅ تم إنشاء ${files.length} ملف في assets/`);
        }

    } catch (error) {
        console.error('❌ خطأ في البناء:', error.message);
        process.exit(1);
    }
}

build();
