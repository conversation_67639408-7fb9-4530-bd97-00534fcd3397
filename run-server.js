// Simple server runner
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Yemen Client Test Server...');

const serverPath = path.join(__dirname, 'server', 'quick-start-test.js');
console.log('📁 Server file:', serverPath);

const server = spawn('node', [serverPath], {
  cwd: path.join(__dirname, 'server'),
  stdio: 'inherit'
});

server.on('error', (err) => {
  console.error('❌ Failed to start server:', err);
});

server.on('close', (code) => {
  console.log(`🛑 Server process exited with code ${code}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  server.kill('SIGINT');
  process.exit(0);
});
