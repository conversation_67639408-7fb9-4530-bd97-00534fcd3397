<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل ربط المطورين - نظام إدارة العملاء اليمني</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .api-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border-right: 5px solid #007bff;
        }
        
        .endpoint {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            margin: 15px 0;
            word-break: break-all;
        }
        
        .test-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .test-form {
            display: grid;
            gap: 15px;
            margin-top: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-group label {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-group input {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .code-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background: #e9ecef;
            border: none;
            border-radius: 8px 8px 0 0;
            cursor: pointer;
            font-weight: bold;
        }
        
        .tab.active {
            background: #007bff;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .credentials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .credentials-table th,
        .credentials-table td {
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }
        
        .credentials-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 دليل ربط المطورين</h1>
            <p>نظام إدارة العملاء اليمني - API Integration Guide</p>
        </div>
        
        <div class="content">
            <div class="api-info">
                <h2>🌐 معلومات API الأساسية</h2>
                <p><strong>الخادم:</strong> http://***********:8081</p>
                <p><strong>نوع المحتوى:</strong> application/json</p>
                <p><strong>طريقة الإرسال:</strong> POST</p>
                
                <div class="endpoint">
                    POST http://***********:8081/api/external/verify-direct
                </div>
            </div>
            
            <div class="test-section">
                <h3>🧪 اختبار API مباشر</h3>
                <p>استخدم النموذج أدناه لاختبار API مباشرة من المتصفح:</p>
                
                <form class="test-form" onsubmit="testAPI(event)">
                    <div class="form-group">
                        <label>اسم المستخدم (الوكيل):</label>
                        <input type="text" id="username" value="testuser" required>
                    </div>
                    
                    <div class="form-group">
                        <label>كلمة المرور:</label>
                        <input type="password" id="password" value="test123" required>
                    </div>
                    
                    <div class="form-group">
                        <label>رمز العميل:</label>
                        <input type="number" id="clientCode" value="1004" required>
                    </div>
                    
                    <div class="form-group">
                        <label>رمز التوكن:</label>
                        <input type="text" id="clientToken" value="TEST1004" required>
                    </div>
                    
                    <div class="form-group">
                        <label>معرف الجهاز:</label>
                        <input type="text" id="deviceId" value="DEV001" required>
                    </div>
                    
                    <button type="submit" class="btn">🚀 اختبار API</button>
                </form>
                
                <div id="result" class="result" style="display: none;"></div>
            </div>
            
            <div class="api-info">
                <h3>📋 بيانات الاختبار</h3>
                <table class="credentials-table">
                    <thead>
                        <tr>
                            <th>النوع</th>
                            <th>المعرف</th>
                            <th>كلمة المرور/التوكن</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>وكيل اختبار</td>
                            <td>testuser</td>
                            <td>test123</td>
                            <td class="status-active">نشط</td>
                        </tr>
                        <tr>
                            <td>عميل 1</td>
                            <td>1004</td>
                            <td>TEST1004</td>
                            <td class="status-active">نشط</td>
                        </tr>
                        <tr>
                            <td>عميل 2</td>
                            <td>1005</td>
                            <td>TEST1005</td>
                            <td class="status-inactive">غير نشط</td>
                        </tr>
                        <tr>
                            <td>عميل 3</td>
                            <td>9999</td>
                            <td>DUMMY999</td>
                            <td class="status-active">نشط</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="code-section">
                <h3>💻 أمثلة الكود</h3>
                
                <div class="tabs">
                    <button class="tab active" onclick="showTab('curl')">cURL</button>
                    <button class="tab" onclick="showTab('javascript')">JavaScript</button>
                    <button class="tab" onclick="showTab('php')">PHP</button>
                    <button class="tab" onclick="showTab('python')">Python</button>
                </div>
                
                <div id="curl" class="tab-content active">
                    <div class="code-block">curl -X POST "http://***********:8081/api/external/verify-direct" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "test123",
    "clientCode": 1004,
    "clientToken": "TEST1004",
    "deviceId": "DEV001"
  }'</div>
                </div>
                
                <div id="javascript" class="tab-content">
                    <div class="code-block">const response = await fetch('http://***********:8081/api/external/verify-direct', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'testuser',
    password: 'test123',
    clientCode: 1004,
    clientToken: 'TEST1004',
    deviceId: 'DEV001'
  })
});

const result = await response.json();
console.log(result);</div>
                </div>
                
                <div id="php" class="tab-content">
                    <div class="code-block">&lt;?php
$data = [
    'username' => 'testuser',
    'password' => 'test123',
    'clientCode' => 1004,
    'clientToken' => 'TEST1004',
    'deviceId' => 'DEV001'
];

$ch = curl_init('http://***********:8081/api/external/verify-direct');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$result = json_decode($response, true);
curl_close($ch);

print_r($result);
?&gt;</div>
                </div>
                
                <div id="python" class="tab-content">
                    <div class="code-block">import requests
import json

url = 'http://***********:8081/api/external/verify-direct'
data = {
    'username': 'testuser',
    'password': 'test123',
    'clientCode': 1004,
    'clientToken': 'TEST1004',
    'deviceId': 'DEV001'
}

response = requests.post(url, json=data)
result = response.json()
print(json.dumps(result, indent=2))</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        async function testAPI(event) {
            event.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result';
            
            const data = {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                clientCode: parseInt(document.getElementById('clientCode').value),
                clientToken: document.getElementById('clientToken').value,
                deviceId: document.getElementById('deviceId').value
            };
            
            try {
                const response = await fetch('http://***********:8081/api/external/verify-direct', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\n\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = response.ok ? 'result success' : 'result error';
                
            } catch (error) {
                resultDiv.textContent = `خطأ في الاتصال: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
