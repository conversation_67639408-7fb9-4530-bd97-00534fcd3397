// تحديث كلمة مرور المستخدم hash8080 إلى hash8080
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

// إعداد Prisma مع رابط قاعدة البيانات
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "postgresql://postgres:yemen123@localhost:5432/yemclient_db"
    }
  }
});

async function updateHash8080Password() {
  console.log('🚀 بدء تحديث كلمة مرور المستخدم hash8080');
  console.log('📅 التوقيت:', new Date().toLocaleString('ar-EG'));
  
  try {
    const loginName = 'hash8080';
    const newPassword = 'hash8080';
    
    console.log(`👤 المستخدم: ${loginName}`);
    console.log(`🔑 كلمة المرور الجديدة: ${newPassword}`);
    
    // البحث عن المستخدم
    console.log('🔍 البحث عن المستخدم في قاعدة البيانات...');
    const user = await prisma.user.findUnique({
      where: { loginName },
      select: {
        id: true,
        username: true,
        loginName: true,
        isActive: true,
        createdAt: true
      }
    });
    
    if (!user) {
      console.error(`❌ المستخدم ${loginName} غير موجود في قاعدة البيانات`);
      
      // عرض قائمة المستخدمين الموجودين
      console.log('📋 المستخدمون الموجودون:');
      const allUsers = await prisma.user.findMany({
        select: { id: true, username: true, loginName: true, isActive: true }
      });
      
      if (allUsers.length === 0) {
        console.log('   لا توجد مستخدمون في قاعدة البيانات');
      } else {
        allUsers.forEach(u => {
          console.log(`   - ${u.loginName} (${u.username}) - ${u.isActive ? 'نشط' : 'غير نشط'}`);
        });
      }
      return;
    }
    
    console.log('✅ تم العثور على المستخدم:');
    console.log(`   الاسم: ${user.username}`);
    console.log(`   اسم الدخول: ${user.loginName}`);
    console.log(`   الحالة: ${user.isActive ? 'نشط' : 'غير نشط'}`);
    console.log(`   تاريخ الإنشاء: ${user.createdAt.toLocaleString('ar-EG')}`);
    
    // تشفير كلمة المرور الجديدة
    console.log('🔐 تشفير كلمة المرور الجديدة...');
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    console.log(`✅ تم التشفير بنجاح`);
    
    // تحديث كلمة المرور
    console.log('💾 تحديث كلمة المرور في قاعدة البيانات...');
    const updatedUser = await prisma.user.update({
      where: { loginName },
      data: { 
        password: hashedPassword,
        updatedAt: new Date()
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        updatedAt: true
      }
    });
    
    console.log('🎉 تم تحديث كلمة المرور بنجاح!');
    console.log(`✅ المستخدم: ${updatedUser.username}`);
    console.log(`✅ اسم الدخول: ${updatedUser.loginName}`);
    console.log(`✅ كلمة المرور الجديدة: ${newPassword}`);
    console.log(`✅ تاريخ التحديث: ${updatedUser.updatedAt.toLocaleString('ar-EG')}`);
    
    // اختبار كلمة المرور الجديدة
    console.log('🧪 اختبار كلمة المرور الجديدة...');
    const isValid = await bcrypt.compare(newPassword, hashedPassword);
    
    if (isValid) {
      console.log('✅ اختبار كلمة المرور نجح - يمكن الآن استخدامها لتسجيل الدخول');
    } else {
      console.log('❌ اختبار كلمة المرور فشل - هناك مشكلة في التشفير');
    }
    
    console.log('\n📋 ملخص العملية:');
    console.log('==================');
    console.log(`المستخدم: ${updatedUser.username}`);
    console.log(`اسم الدخول: ${updatedUser.loginName}`);
    console.log(`كلمة المرور الجديدة: ${newPassword}`);
    console.log(`حالة الاختبار: ${isValid ? '✅ صحيحة' : '❌ خطأ'}`);
    console.log(`وقت التحديث: ${updatedUser.updatedAt.toLocaleString('ar-EG')}`);
    console.log('\n🎯 يمكنك الآن تسجيل الدخول باستخدام:');
    console.log(`   اسم المستخدم: ${loginName}`);
    console.log(`   كلمة المرور: ${newPassword}`);
    
  } catch (error) {
    console.error('❌ حدث خطأ أثناء تحديث كلمة المرور:', error);
    
    if (error.code === 'P2025') {
      console.error('💡 السبب: المستخدم غير موجود');
    } else if (error.code === 'P1001') {
      console.error('💡 السبب: لا يمكن الاتصال بقاعدة البيانات');
      console.error('   تأكد من تشغيل PostgreSQL وصحة إعدادات الاتصال');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 السبب: قاعدة البيانات غير متاحة');
      console.error('   تأكد من تشغيل PostgreSQL على المنفذ 5432');
    } else {
      console.error('💡 تفاصيل الخطأ:', error.message);
    }
  } finally {
    console.log('🔌 إغلاق الاتصال بقاعدة البيانات...');
    await prisma.$disconnect();
    console.log('✅ تم إغلاق الاتصال بنجاح');
  }
}

// تشغيل الدالة
console.log('🔧 سكريبت تحديث كلمة مرور المستخدم hash8080');
console.log('=' .repeat(50));

updateHash8080Password()
  .then(() => {
    console.log('\n🏁 انتهت العملية بنجاح');
    console.log('يمكنك الآن إغلاق هذه النافذة');
  })
  .catch((error) => {
    console.error('\n💥 فشلت العملية:', error.message);
    console.log('تحقق من الأخطاء أعلاه وحاول مرة أخرى');
  });
