<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المطورين - نظام إدارة العملاء اليمني</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .test-button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .api-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار API المطورين</h1>
            <p>نظام إدارة العملاء اليمني - واجهة المطورين الخارجية</p>
        </div>
        
        <div class="content">
            <div class="api-info">
                <h3>📋 معلومات الخادم</h3>
                <p><strong>الخادم:</strong> http://***********:8080</p>
                <p><strong>بيانات الاختبار:</strong> testuser / test123</p>
                <p><strong>رموز العملاء:</strong> 1004+TEST1004, 1005+TEST1005, 9999+DUMMY999</p>
            </div>

            <!-- اختبار الحماية -->
            <div class="test-section">
                <h3>🛡️ اختبار الحماية - APIs المحمية (يجب أن ترجع 401)</h3>
                <button class="test-button danger" onclick="testProtectedAPI('/api/users?page=1&limit=10')">اختبار المستخدمين</button>
                <button class="test-button danger" onclick="testProtectedAPI('/api/clients?page=1&limit=10')">اختبار العملاء</button>
                <button class="test-button danger" onclick="testProtectedAPI('/api/agents?page=1&limit=10')">اختبار الوكلاء</button>
                <button class="test-button danger" onclick="testProtectedAPI('/api/data-records?page=1&limit=10')">اختبار البيانات</button>
                <div id="protectedResult" class="result" style="display:none;"></div>
            </div>

            <!-- اختبار APIs العامة -->
            <div class="test-section">
                <h3>✅ اختبار APIs العامة للمطورين (يجب أن تعمل)</h3>
                <button class="test-button success" onclick="testPublicAPI('/api/external/health')">فحص الصحة</button>
                <button class="test-button success" onclick="testPublicAPI('/api/external/test-db')">اختبار قاعدة البيانات</button>
                <button class="test-button success" onclick="testPublicAPI('/api/external/test-data')">بيانات الاختبار</button>
                <div id="publicResult" class="result" style="display:none;"></div>
            </div>

            <!-- اختبار التحقق المباشر -->
            <div class="test-section">
                <h3>🔐 اختبار التحقق المباشر للمطورين</h3>
                <button class="test-button" onclick="testDirectVerification()">اختبار التحقق المباشر</button>
                <div id="verifyResult" class="result" style="display:none;"></div>
                
                <div class="code-block">
POST /api/external/verify-direct
Content-Type: application/json

{
  "agent_username": "testuser",
  "agent_password": "test123",
  "client_code": "1004",
  "client_token": "TEST1004",
  "device_id": "TEST_DEVICE_001"
}
                </div>
            </div>

            <!-- أمثلة الكود -->
            <div class="test-section">
                <h3>💻 أمثلة الكود للمطورين</h3>
                
                <h4>JavaScript/Node.js:</h4>
                <div class="code-block">
const axios = require('axios');

// اختبار التحقق المباشر
const response = await axios.post('http://***********:8080/api/external/verify-direct', {
  agent_username: 'testuser',
  agent_password: 'test123',
  client_code: '1004',
  client_token: 'TEST1004',
  device_id: 'TEST_DEVICE_001'
});

console.log(response.data);
                </div>

                <h4>cURL:</h4>
                <div class="code-block">
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_username": "testuser",
    "agent_password": "test123",
    "client_code": "1004",
    "client_token": "TEST1004",
    "device_id": "TEST_DEVICE_001"
  }'
                </div>
            </div>
        </div>
    </div>

    <script>
        const SERVER_URL = 'http://***********:8080';

        async function testProtectedAPI(endpoint) {
            const resultDiv = document.getElementById('protectedResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = `🔄 اختبار ${endpoint}...`;
            resultDiv.className = 'result info';

            try {
                const response = await fetch(SERVER_URL + endpoint, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.text();
                
                if (response.status === 401) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ محمي بشكل صحيح!\nStatus: ${response.status}\nResponse: ${data}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ غير محمي! يعرض البيانات!\nStatus: ${response.status}\nResponse: ${data.substring(0, 500)}...`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }

        async function testPublicAPI(endpoint) {
            const resultDiv = document.getElementById('publicResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = `🔄 اختبار ${endpoint}...`;
            resultDiv.className = 'result info';

            try {
                const response = await fetch(SERVER_URL + endpoint, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.text();
                
                if (response.status === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ يعمل بشكل صحيح!\nStatus: ${response.status}\nResponse: ${data}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ لا يعمل!\nStatus: ${response.status}\nResponse: ${data}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }

        async function testDirectVerification() {
            const resultDiv = document.getElementById('verifyResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 اختبار التحقق المباشر...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch(SERVER_URL + '/api/external/verify-direct', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_username: 'testuser',
                        agent_password: 'test123',
                        client_code: '1004',
                        client_token: 'TEST1004',
                        device_id: 'TEST_DEVICE_001'
                    })
                });

                const data = await response.text();
                
                if (response.status === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ التحقق نجح!\nStatus: ${response.status}\nResponse: ${data}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ التحقق فشل!\nStatus: ${response.status}\nResponse: ${data}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('🚀 صفحة اختبار API المطورين جاهزة');
            console.log('📡 الخادم:', SERVER_URL);
        };
    </script>
</body>
</html>
