// فحص البيانات الموجودة في قاعدة البيانات
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabaseData() {
  try {
    console.log('🔍 فحص البيانات الموجودة في قاعدة البيانات...\n');

    // فحص الوكلاء
    console.log('👥 الوكلاء الموجودين:');
    console.log('==================');
    const agents = await prisma.agent.findMany({
      select: {
        id: true,
        agentName: true,
        loginName: true,
        isActive: true
      }
    });

    if (agents.length > 0) {
      agents.forEach(agent => {
        console.log(`ID: ${agent.id} | اسم الدخول: ${agent.loginName || 'غير محدد'} | الاسم: ${agent.agentName} | نشط: ${agent.isActive ? 'نعم' : 'لا'}`);
      });
    } else {
      console.log('❌ لا يوجد وكلاء في قاعدة البيانات');
    }

    // فحص المستخدمين
    console.log('\n👤 المستخدمين الموجودين:');
    console.log('=======================');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        isActive: true
      }
    });

    if (users.length > 0) {
      users.forEach(user => {
        console.log(`ID: ${user.id} | اسم الدخول: ${user.loginName} | الاسم: ${user.username} | نشط: ${user.isActive ? 'نعم' : 'لا'}`);
      });
    } else {
      console.log('❌ لا يوجد مستخدمين في قاعدة البيانات');
    }

    // فحص العملاء
    console.log('\n💼 العملاء الموجودين:');
    console.log('===================');
    const clients = await prisma.client.findMany({
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        token: true,
        status: true
      }
    });

    if (clients.length > 0) {
      clients.forEach(client => {
        console.log(`كود العميل: ${client.clientCode} | الاسم: ${client.clientName} | التوكن: ${client.token || 'غير محدد'} | الحالة: ${client.status === 1 ? 'نشط' : 'غير نشط'}`);
      });
    } else {
      console.log('❌ لا يوجد عملاء في قاعدة البيانات');
    }

    // اقتراح بيانات للاختبار
    console.log('\n💡 اقتراحات للاختبار:');
    console.log('===================');
    
    if (agents.length > 0 && agents.some(a => a.loginName && a.isActive)) {
      const activeAgent = agents.find(a => a.loginName && a.isActive);
      console.log(`🔐 استخدم الوكيل: ${activeAgent.loginName}`);
      console.log('   (تحتاج لمعرفة كلمة المرور من قاعدة البيانات)');
    }
    
    if (users.length > 0 && users.some(u => u.isActive)) {
      const activeUser = users.find(u => u.isActive);
      console.log(`🔐 أو استخدم المستخدم: ${activeUser.loginName}`);
      console.log('   (تحتاج لمعرفة كلمة المرور من قاعدة البيانات)');
    }

    if (clients.length > 0 && clients.some(c => c.status === 1)) {
      const activeClient = clients.find(c => c.status === 1);
      console.log(`💼 مع العميل: ${activeClient.clientCode} | التوكن: ${activeClient.token}`);
    }

  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseData();
