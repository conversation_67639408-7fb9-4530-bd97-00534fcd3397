<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقول معرف الجهاز</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus {
            border-color: #1976d2;
            outline: none;
        }
        
        .success {
            background-color: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            color: #2e7d32;
        }
        
        .info {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            color: #1565c0;
        }
        
        .button {
            background-color: #1976d2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-left: 10px;
        }
        
        .button:hover {
            background-color: #1565c0;
        }
        
        .log {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار حقول معرف الجهاز</h1>
        
        <div class="success">
            ✅ تم إضافة حقل "معرف الجهاز 2" بنجاح في النظام!
        </div>
        
        <div class="info">
            📋 هذه الصفحة لاختبار عمل الحقول الجديدة. في النظام الحقيقي، ستظهر هذه الحقول في صفحات إضافة وتعديل المستخدمين.
        </div>
        
        <form id="testForm">
            <div class="form-group">
                <label for="deviceId">معرف الجهاز 1 (اختياري):</label>
                <input type="text" id="deviceId" name="deviceId" placeholder="أدخل معرف الجهاز الأول">
            </div>
            
            <div class="form-group">
                <label for="device1">معرف الجهاز 2 (اختياري):</label>
                <input type="text" id="device1" name="device1" placeholder="أدخل معرف الجهاز الثاني">
            </div>
            
            <button type="button" class="button" onclick="testSave()">اختبار الحفظ</button>
            <button type="button" class="button" onclick="clearLog()">مسح السجل</button>
        </form>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function testSave() {
            const deviceId = document.getElementById('deviceId').value;
            const device1 = document.getElementById('device1').value;
            
            log('🔍 اختبار حفظ البيانات:');
            log(`   معرف الجهاز 1: "${deviceId}"`);
            log(`   معرف الجهاز 2: "${device1}"`);
            
            // محاكاة إرسال البيانات
            const data = {
                deviceId: deviceId,
                device1: device1
            };
            
            log('📤 البيانات المرسلة: ' + JSON.stringify(data, null, 2));
            log('✅ تم الاختبار بنجاح!');
            log('');
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // تسجيل تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 تم تحميل صفحة الاختبار');
            log('🔧 الحقول متاحة للاختبار');
            log('');
        });
        
        // مراقبة تغييرات الحقول
        document.getElementById('deviceId').addEventListener('input', function(e) {
            log(`📝 تحديث معرف الجهاز 1: "${e.target.value}"`);
        });
        
        document.getElementById('device1').addEventListener('input', function(e) {
            log(`📝 تحديث معرف الجهاز 2: "${e.target.value}"`);
        });
    </script>
</body>
</html>
