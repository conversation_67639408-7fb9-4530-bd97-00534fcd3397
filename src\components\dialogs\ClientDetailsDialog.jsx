import React from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Grid,
  Typography,
  Box,
  Chip,
  Divider,
  Card,
  CardContent,
  IconButton
} from '@mui/material'
import {
  Person as PersonIcon,
  Apps as AppsIcon,
  CreditCard as CreditCardIcon,
  Computer as ComputerIcon,
  VpnKey as VpnKeyIcon,
  AccountCircle as AccountCircleIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material'

const ClientDetailsDialog = ({ open, onClose, client }) => {
  if (!client) return null

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontSize: '1.5rem',
        fontWeight: 'bold'
      }}>
        <span>📋 تفاصيل العميل</span>
        <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
          <span style={{ fontSize: '20px' }}>✕</span>
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* معلومات العميل الأساسية */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PersonIcon /> معلومات العميل الأساسية
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '100px' }}>
                        <strong>رمز العميل:</strong>
                      </Typography>
                      <Chip
                        label={client.clientCode}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '100px' }}>
                        <strong>اسم العميل:</strong>
                      </Typography>
                      <Typography variant="body2">{client.clientName}</Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <AppsIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '100px' }}>
                        <strong>اسم التطبيق:</strong>
                      </Typography>
                      <Typography variant="body2">{client.appName}</Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '100px' }}>
                        <strong>الحالة:</strong>
                      </Typography>
                      <Chip
                        label={client.status === 1 ? 'مفعل' : 'محظور'}
                        color={client.status === 1 ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات الأمان والوصول */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#d35400', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <VpnKeyIcon /> معلومات الأمان والوصول
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <CreditCardIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>رقم البطاقة:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                        {client.cardNumber}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <ComputerIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>عنوان IP:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                        {client.ipAddress}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <VpnKeyIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>كلمة المرور:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                        {'•'.repeat(client.password?.length || 8)}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات المستخدم الذي أنشأ الحساب */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AccountCircleIcon /> المستخدم الذي أنشأ الحساب
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>اسم المستخدم:</strong>
                      </Typography>
                      <Typography variant="body2">
                        {client.user?.username || 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>رقم المستخدم:</strong>
                      </Typography>
                      <Chip
                        label={client.userId || client.user?.id || 'غير محدد'}
                        color="secondary"
                        variant="outlined"
                        size="small"
                      />
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات التواريخ */}
          <Grid item xs={12}>
            <Card sx={{ background: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#8e44ad', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CalendarIcon /> معلومات التواريخ
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>تاريخ الإنشاء:</strong>
                      </Typography>
                      <Typography variant="body2">
                        {formatDate(client.createdAt)}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>آخر تحديث:</strong>
                      </Typography>
                      <Typography variant="body2">
                        {formatDate(client.updatedAt)}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3, justifyContent: 'center' }}>
        <Button
          onClick={onClose}
          variant="contained"
          sx={{
            minWidth: '120px',
            borderRadius: '25px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
            }
          }}
        >
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ClientDetailsDialog
