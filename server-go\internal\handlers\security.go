package handlers

import (
	"encoding/json"
	"net/http"
	
	"yemclient-server/internal/database"
	"yemclient-server/internal/models"
)

func GetSecurityStatsHandler(w http.ResponseWriter, r *http.Request) {
	// Simplified stats - in real app would query database
	stats := map[string]interface{}{
		"totalLogins":       100,
		"failedAttempts":    5,
		"blockedIPs":        2,
		"lastLoginAttempt": "2023-05-15T10:30:00Z",
	}
	json.NewEncoder(w).Encode(stats)
}

func GetLoginAttemptsHandler(w http.ResponseWriter, r *http.Request) {
	var attempts []models.LoginAttempt
	if err := database.DB.Find(&attempts).Error; err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(attempts)
}

func BlockIPHandler(w http.ResponseWriter, r *http.Request) {
	var req struct {
		IP string `json:"ip"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	
	// In real app would save to database
	json.NewEncoder(w).Encode(map[string]string{
		"message": "IP blocked successfully",
		"ip":      req.IP,
	})
}

func UnblockIPHandler(w http.ResponseWriter, r *http.Request) {
	var req struct {
		IP string `json:"ip"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	
	// In real app would remove from database
	json.NewEncoder(w).Encode(map[string]string{
		"message": "IP unblocked successfully",
		"ip":      req.IP,
	})
}
