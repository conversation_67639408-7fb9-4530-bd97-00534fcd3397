# Yemen Client Test Server Launcher
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Yemen Client Management System" -ForegroundColor Yellow
Write-Host "        Test Server Launcher" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Change to script directory
Set-Location $PSScriptRoot

Write-Host "🚀 Starting Quick Test Server..." -ForegroundColor Yellow
Write-Host "📍 Server will be available at: http://localhost:8080" -ForegroundColor Cyan
Write-Host "🔗 Login page: http://localhost:8080/login" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Gray
Write-Host ""

# Start the server
try {
    node quick-start-test.js
} catch {
    Write-Host "❌ Error starting server: $_" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "Server stopped." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
}
