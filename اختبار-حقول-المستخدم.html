<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار حقول المستخدم الجديدة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        .test-section {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s;
        }
        .test-button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .test-button.secondary {
            background: #2196F3;
        }
        .test-button.secondary:hover {
            background: #1976D2;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .info { background: #2196F3; }
        .warning { background: #ff9800; }
        .code-block {
            background: rgba(0,0,0,0.6);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .field-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .field-box {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            border: 2px solid rgba(255,255,255,0.3);
        }
        .field-label {
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 8px;
        }
        .field-input {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 5px;
            background: rgba(0,0,0,0.3);
            color: white;
            font-size: 14px;
        }
        .field-help {
            font-size: 12px;
            color: #ccc;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار حقول المستخدم الجديدة</h1>
            <p>تم إضافة حقل "معرف الجهاز 2" وتعديل اسم الحقل السابق إلى "معرف الجهاز 1"</p>
        </div>

        <div class="test-section">
            <h2>📋 عرض الحقول الجديدة</h2>
            <p>هكذا ستظهر الحقول في نموذج إضافة/تعديل المستخدم:</p>
            
            <div class="field-demo">
                <div class="field-box">
                    <div class="field-label">معرف الجهاز 1 (اختياري)</div>
                    <input type="text" class="field-input" placeholder="device_abc123_1234567890" readonly>
                    <div class="field-help">سيتم تعيينه تلقائياً عند أول تسجيل دخول</div>
                </div>
                
                <div class="field-box">
                    <div class="field-label">معرف الجهاز 2 (اختياري)</div>
                    <input type="text" class="field-input" placeholder="device_def456_0987654321" readonly>
                    <div class="field-help">معرف الجهاز الثاني للمستخدم</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 التعديلات المطبقة</h2>
            <div class="code-block">
✅ إضافة device1 إلى schema التحقق
✅ إضافة device1 إلى القيم الافتراضية  
✅ تعديل label من "معرف الجهاز" إلى "معرف الجهاز 1"
✅ إضافة حقل جديد "معرف الجهاز 2" 
✅ ربط الحقل الجديد بـ device1 في قاعدة البيانات
✅ تحديث منطق إعادة التعيين للتعديل
✅ تطبيق التعديلات على كلا الملفين (client/src و src)
            </div>
        </div>

        <div class="test-section">
            <h2>🗄️ قاعدة البيانات</h2>
            <p>الحقول في جدول المستخدمين:</p>
            <div class="code-block">
deviceId (device_id) → معرف الجهاز 1
device1 (device1)    → معرف الجهاز 2 ← الحقل الجديد
            </div>
            <p><strong>ملاحظة:</strong> الحقل الجديد "معرف الجهاز 2" يحفظ في عمود <code>device1</code> كما طلبت.</p>
        </div>

        <div class="test-section">
            <h2>🧪 اختبار API</h2>
            <button class="test-button" onclick="testUserAPI()">🔍 اختبار جلب المستخدمين</button>
            <button class="test-button secondary" onclick="testCreateUser()">➕ اختبار إنشاء مستخدم</button>
            <div id="api-results"></div>
        </div>

        <div class="test-section">
            <h2>📝 خطوات الاختبار اليدوي</h2>
            <ol>
                <li><strong>افتح النظام:</strong> اذهب إلى صفحة المستخدمين</li>
                <li><strong>انقر "إضافة مستخدم":</strong> يجب أن ترى الحقلين الجديدين</li>
                <li><strong>تحقق من التسميات:</strong>
                    <ul>
                        <li>الحقل الأول: "معرف الجهاز 1 (اختياري)"</li>
                        <li>الحقل الثاني: "معرف الجهاز 2 (اختياري)"</li>
                    </ul>
                </li>
                <li><strong>اختبر الحفظ:</strong> أدخل بيانات واحفظ</li>
                <li><strong>اختبر التعديل:</strong> عدل مستخدم موجود وتأكد من ظهور القيم</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🎯 النتيجة المتوقعة</h2>
            <div class="result success">
✅ حقل "معرف الجهاز 1" يحفظ في deviceId
✅ حقل "معرف الجهاز 2" يحفظ في device1  
✅ كلا الحقلين اختياريين
✅ النموذج يعمل للإضافة والتعديل
✅ البيانات تحفظ وتسترجع بشكل صحيح
            </div>
        </div>
    </div>

    <script>
        async function testUserAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<div class="result info">🔄 جاري اختبار API...</div>';

            try {
                const response = await fetch('http://***********:8080/api/users?page=1&limit=5');
                
                if (response.status === 401) {
                    resultsDiv.innerHTML = `
                        <div class="result warning">
                            <h4>🔒 API محمي (هذا صحيح!)</h4>
                            <p>Status: ${response.status}</p>
                            <p>تحتاج JWT Token للوصول إلى بيانات المستخدمين</p>
                        </div>
                    `;
                } else if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ API يعمل</h4>
                            <p>Status: ${response.status}</p>
                            <p>عدد المستخدمين: ${data.data?.length || 0}</p>
                            <details>
                                <summary>عرض البيانات</summary>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ خطأ في API</h4>
                            <p>Status: ${response.status}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ خطأ في الاتصال</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testCreateUser() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<div class="result info">🔄 اختبار إنشاء مستخدم (بدون token)...</div>';

            const testUser = {
                username: 'مستخدم تجريبي',
                loginName: 'testuser123',
                password: 'password123',
                deviceId: 'device_test_001',
                device1: 'device_test_002',
                permissions: {
                    isAdmin: false,
                    clients: { create: false, read: true, update: false, delete: false }
                }
            };

            try {
                const response = await fetch('http://***********:8080/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testUser)
                });

                if (response.status === 401) {
                    resultsDiv.innerHTML = `
                        <div class="result warning">
                            <h4>🔒 API محمي (هذا صحيح!)</h4>
                            <p>Status: ${response.status}</p>
                            <p>تحتاج JWT Token لإنشاء مستخدمين</p>
                            <p><strong>البيانات المرسلة تحتوي على:</strong></p>
                            <pre>deviceId: "${testUser.deviceId}"
device1: "${testUser.device1}"</pre>
                        </div>
                    `;
                } else {
                    const data = await response.json();
                    resultsDiv.innerHTML = `
                        <div class="result ${response.ok ? 'success' : 'error'}">
                            <h4>${response.ok ? '✅' : '❌'} نتيجة الإنشاء</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ خطأ في الاتصال</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('🎯 صفحة اختبار حقول المستخدم جاهزة');
        };
    </script>
</body>
</html>
