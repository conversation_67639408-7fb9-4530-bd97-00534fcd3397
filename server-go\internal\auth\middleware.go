package auth

import (
	"context"
	"net/http"
	"strings"
)

// AuthMiddleware verifies JWT token
func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip auth for certain endpoints
		if skipAuth(r) {
			next.ServeHTTP(w, r)
			return
		}

		tokenString := extractToken(r)
		if tokenString == "" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		claims, err := ValidateToken(tokenString)
		if err != nil {
			http.Error(w, "Invalid token", http.StatusUnauthorized)
			return
		}

		// Add user ID to context
		ctx := context.WithValue(r.Context(), "userID", claims.UserID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func skipAuth(r *http.Request) bool {
	publicRoutes := []string{
		"/health",
		"/api/auth/login",
	}

	for _, route := range publicRoutes {
		if r.URL.Path == route {
			return true
		}
	}
	return false
}

func extractToken(r *http.Request) string {
	// 1. Check Authorization header
	bearerToken := r.Header.Get("Authorization")
	if len(bearerToken) > 7 && strings.ToUpper(bearerToken[0:6]) == "BEARER " {
		return bearerToken[7:]
	}

	// 2. Check URL query parameter
	if token := r.URL.Query().Get("token"); token != "" {
		return token
	}

	// 3. Check cookie
	cookie, err := r.Cookie("token")
	if err == nil {
		return cookie.Value
	}

	return ""
}
