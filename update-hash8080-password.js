// تحديث كلمة مرور المستخدم hash8080
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

// إعداد Prisma
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "postgresql://postgres:yemen123@localhost:5432/yemclient_db"
    }
  }
});

async function updateHash8080Password() {
  try {
    console.log('🔧 بدء تحديث كلمة مرور المستخدم hash8080...');
    console.log('📅 الوقت:', new Date().toLocaleString('ar-EG'));
    
    const loginName = 'hash8080';
    const newPassword = 'hash8080';
    
    console.log(`👤 المستخدم: ${loginName}`);
    console.log(`🔑 كلمة المرور الجديدة: ${newPassword}`);
    
    // البحث عن المستخدم أولاً
    console.log('🔍 البحث عن المستخدم...');
    const user = await prisma.user.findUnique({
      where: { loginName },
      select: {
        id: true,
        username: true,
        loginName: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    if (!user) {
      console.error(`❌ لم يتم العثور على المستخدم: ${loginName}`);
      console.log('📋 قائمة المستخدمين الموجودين:');
      
      const allUsers = await prisma.user.findMany({
        select: {
          id: true,
          username: true,
          loginName: true,
          isActive: true
        }
      });
      
      allUsers.forEach(u => {
        console.log(`   - ID: ${u.id}, Username: ${u.username}, LoginName: ${u.loginName}, Active: ${u.isActive}`);
      });
      
      return;
    }
    
    console.log(`✅ تم العثور على المستخدم:`);
    console.log(`   - ID: ${user.id}`);
    console.log(`   - الاسم: ${user.username}`);
    console.log(`   - اسم الدخول: ${user.loginName}`);
    console.log(`   - نشط: ${user.isActive}`);
    console.log(`   - تاريخ الإنشاء: ${user.createdAt.toLocaleString('ar-EG')}`);
    
    // تشفير كلمة المرور الجديدة
    console.log('🔐 تشفير كلمة المرور الجديدة...');
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    console.log(`✅ تم تشفير كلمة المرور (${hashedPassword.substring(0, 20)}...)`);
    
    // تحديث كلمة المرور
    console.log('💾 تحديث كلمة المرور في قاعدة البيانات...');
    const updatedUser = await prisma.user.update({
      where: { loginName },
      data: { 
        password: hashedPassword,
        updatedAt: new Date()
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        updatedAt: true
      }
    });
    
    console.log('🎉 تم تحديث كلمة المرور بنجاح!');
    console.log(`✅ المستخدم: ${updatedUser.username}`);
    console.log(`✅ اسم الدخول: ${updatedUser.loginName}`);
    console.log(`✅ كلمة المرور الجديدة: ${newPassword}`);
    console.log(`✅ تاريخ التحديث: ${updatedUser.updatedAt.toLocaleString('ar-EG')}`);
    
    // اختبار كلمة المرور الجديدة
    console.log('🧪 اختبار كلمة المرور الجديدة...');
    const isPasswordCorrect = await bcrypt.compare(newPassword, hashedPassword);
    
    if (isPasswordCorrect) {
      console.log('✅ اختبار كلمة المرور نجح - يمكن الآن تسجيل الدخول');
    } else {
      console.log('❌ اختبار كلمة المرور فشل - هناك مشكلة');
    }
    
    console.log('\n📋 ملخص التحديث:');
    console.log('================');
    console.log(`المستخدم: ${updatedUser.username}`);
    console.log(`اسم الدخول: ${updatedUser.loginName}`);
    console.log(`كلمة المرور: ${newPassword}`);
    console.log(`حالة الاختبار: ${isPasswordCorrect ? '✅ نجح' : '❌ فشل'}`);
    console.log(`تاريخ التحديث: ${updatedUser.updatedAt.toLocaleString('ar-EG')}`);
    
  } catch (error) {
    console.error('❌ خطأ في تحديث كلمة المرور:', error);
    
    if (error.code === 'P2025') {
      console.error('💡 السبب: المستخدم غير موجود في قاعدة البيانات');
    } else if (error.code === 'P1001') {
      console.error('💡 السبب: لا يمكن الاتصال بقاعدة البيانات');
      console.error('   تأكد من أن PostgreSQL يعمل وأن إعدادات الاتصال صحيحة');
    } else {
      console.error('💡 تفاصيل الخطأ:', error.message);
    }
  } finally {
    console.log('🔌 إغلاق الاتصال بقاعدة البيانات...');
    await prisma.$disconnect();
    console.log('✅ تم إغلاق الاتصال');
  }
}

// تشغيل الدالة
updateHash8080Password()
  .then(() => {
    console.log('\n🏁 انتهت العملية بنجاح');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 فشلت العملية:', error);
    process.exit(1);
  });
