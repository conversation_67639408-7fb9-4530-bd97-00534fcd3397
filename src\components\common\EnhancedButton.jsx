import React from 'react';
import { Button, IconButton, Tooltip } from '@mui/material';
import IconWithFallback from './IconWithFallback';

const EnhancedButton = ({
  icon,
  iconName,
  children,
  tooltip,
  variant = 'contained',
  size = 'medium',
  color = 'primary',
  isIconButton = false,
  fallbackText,
  onClick,
  sx,
  ...otherProps
}) => {
  const buttonContent = (
    <>
      {icon && (
        <IconWithFallback
          icon={icon}
          fallbackName={iconName}
          fontSize={size === 'small' ? 'small' : size === 'large' ? 'large' : 'medium'}
        />
      )}
      {children && (
        <span style={{ marginRight: icon ? '8px' : '0' }}>
          {children}
        </span>
      )}
      {!children && fallbackText && (
        <span style={{ marginRight: icon ? '8px' : '0', fontSize: '0.875rem' }}>
          {fallbackText}
        </span>
      )}
    </>
  );

  const buttonElement = isIconButton ? (
    <IconButton
      size={size}
      color={color}
      onClick={onClick}
      sx={{
        '&:hover': {
          backgroundColor: `${color}.light`,
          color: 'white'
        },
        ...sx
      }}
      {...otherProps}
    >
      {buttonContent}
    </IconButton>
  ) : (
    <Button
      variant={variant}
      size={size}
      color={color}
      onClick={onClick}
      startIcon={icon ? (
        <IconWithFallback
          icon={icon}
          fallbackName={iconName}
          fontSize={size === 'small' ? 'small' : 'medium'}
        />
      ) : null}
      sx={{
        minWidth: children ? '140px' : 'auto',
        '& .MuiButton-startIcon': {
          marginRight: '8px',
          marginLeft: '-4px'
        },
        ...sx
      }}
      {...otherProps}
    >
      {children || fallbackText}
    </Button>
  );

  return tooltip ? (
    <Tooltip title={tooltip} arrow>
      {buttonElement}
    </Tooltip>
  ) : buttonElement;
};

export default EnhancedButton;
