import React, { useEffect } from 'react'
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Divider,
  Card,
  CardContent
} from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useMutation, useQuery } from 'react-query'
import { useAuth } from '../../contexts/AuthContext'
import { useSnackbar } from 'notistack'

const schema = yup.object({
  agentId: yup.number().required('الوكيل مطلوب'),
  clientId: yup.number().required('العميل مطلوب'),
  agentReference: yup.number().required('رقم مرجع الوكيل مطلوب'),
  operationStatus: yup.number().oneOf([0, 1], 'حالة العملية يجب أن تكون 0 أو 1').required('حالة العملية مطلوبة'),
  clientCode: yup.string().required('رمز العميل مطلوب'),
  clientPassword: yup.string().required('كلمة مرور العميل مطلوبة'),
  clientIpAddress: yup.string()
    .required('عنوان IP العميل مطلوب')
    .matches(
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      'عنوان IP غير صحيح'
    ),
  operationDate: yup.date().nullable()
})

const DataRecordForm = ({ record, onSuccess, readOnly = false }) => {
  const { api } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      agentId: '',
      clientId: '',
      agentReference: '',
      operationStatus: 1,
      clientCode: '',
      clientPassword: '',
      clientIpAddress: '',
      operationDate: null
    }
  })

  // بيانات ثابتة للوكلاء (بدون API)
  const agents = [
    {
      id: 1,
      agentName: 'وكيل تجريبي 1',
      agencyName: 'وكالة تجريبية 1'
    },
    {
      id: 2,
      agentName: 'وكيل تجريبي 2',
      agencyName: 'وكالة تجريبية 2'
    }
  ]

  // بيانات ثابتة للعملاء (بدون API)
  const clients = [
    {
      id: 1,
      name: 'عميل تجريبي 1',
      code: 'CLIENT001'
    },
    {
      id: 2,
      name: 'عميل تجريبي 2',
      code: 'CLIENT002'
    }
  ]

  // إضافة/تحديث سجل البيانات
  const mutation = useMutation(
    (data) => {
      if (record) {
        return api.put(`/api/data-records/${record.id}`, data)
      } else {
        return api.post('/api/data-records', data)
      }
    },
    {
      onSuccess: () => {
        enqueueSnackbar(
          record ? 'تم تحديث السجل بنجاح' : 'تم إضافة السجل بنجاح',
          { variant: 'success' }
        )
        onSuccess?.()
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.error || 'حدث خطأ أثناء حفظ السجل',
          { variant: 'error' }
        )
      }
    }
  )

  // تحديث النموذج عند تغيير السجل
  useEffect(() => {
    if (record) {
      reset({
        agentId: record.agentId || '',
        clientId: record.clientId || '',
        agentReference: record.agentReference || '',
        operationStatus: record.operationStatus ?? 1,
        clientCode: record.clientCode || '',
        clientPassword: record.clientPassword || '',
        clientIpAddress: record.clientIpAddress || '',
        operationDate: record.operationDate ? new Date(record.operationDate).toISOString().slice(0, 16) : null
      })
    }
  }, [record, reset])

  const onSubmit = (data) => {
    const submitData = {
      ...data,
      agentId: parseInt(data.agentId),
      clientId: parseInt(data.clientId),
      agentReference: parseInt(data.agentReference),
      operationStatus: parseInt(data.operationStatus),
      operationDate: data.operationDate ? new Date(data.operationDate).toISOString() : new Date().toISOString()
    }
    mutation.mutate(submitData)
  }

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* معلومات العملية */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                📋 معلومات العملية
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="agentId"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.agentId}>
                        <InputLabel>الوكيل</InputLabel>
                        <Select
                          {...field}
                          label="الوكيل"
                          disabled={readOnly}
                        >
                          {agents?.map((agent) => (
                            <MenuItem key={agent.id} value={agent.id}>
                              {agent.agentName} - {agent.agencyName}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.agentId && (
                          <Typography variant="caption" color="error">
                            {errors.agentId.message}
                          </Typography>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="clientId"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.clientId}>
                        <InputLabel>العميل</InputLabel>
                        <Select
                          {...field}
                          label="العميل"
                          disabled={readOnly}
                        >
                          {clients?.map((client) => (
                            <MenuItem key={client.id} value={client.id}>
                              {client.name} - {client.code}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.clientId && (
                          <Typography variant="caption" color="error">
                            {errors.clientId.message}
                          </Typography>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="agentReference"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="رقم مرجع الوكيل"
                        type="number"
                        error={!!errors.agentReference}
                        helperText={errors.agentReference?.message}
                        disabled={readOnly}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="operationStatus"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.operationStatus}>
                        <InputLabel>حالة العملية</InputLabel>
                        <Select
                          {...field}
                          label="حالة العملية"
                          disabled={readOnly}
                        >
                          <MenuItem value={1}>ناجحة</MenuItem>
                          <MenuItem value={0}>فاشلة</MenuItem>
                        </Select>
                        {errors.operationStatus && (
                          <Typography variant="caption" color="error">
                            {errors.operationStatus.message}
                          </Typography>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="operationDate"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="تاريخ العملية"
                        type="datetime-local"
                        InputLabelProps={{ shrink: true }}
                        disabled={readOnly}
                        helperText="اتركه فارغاً لاستخدام التاريخ الحالي"
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* بيانات العميل */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom color="secondary">
                👤 بيانات العميل
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Controller
                    name="clientCode"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="رمز العميل"
                        error={!!errors.clientCode}
                        helperText={errors.clientCode?.message}
                        disabled={readOnly}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <Controller
                    name="clientPassword"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="كلمة مرور العميل"
                        type="password"
                        error={!!errors.clientPassword}
                        helperText={errors.clientPassword?.message}
                        disabled={readOnly}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <Controller
                    name="clientIpAddress"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="عنوان IP العميل"
                        placeholder="*************"
                        error={!!errors.clientIpAddress}
                        helperText={errors.clientIpAddress?.message}
                        disabled={readOnly}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* أزرار الحفظ */}
        {!readOnly && (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                type="submit"
                variant="contained"
                disabled={mutation.isLoading}
                sx={{ minWidth: 120 }}
              >
                {mutation.isLoading ? 'جاري الحفظ...' : (record ? 'تحديث' : 'حفظ')}
              </Button>
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  )
}

export default DataRecordForm
