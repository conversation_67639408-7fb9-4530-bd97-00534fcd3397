# 🚀 التشغيل السريع - خدمة Remote Agent

## ⚡ البدء السريع (3 خطوات فقط)

### 1️⃣ التشغيل التلقائي
```bash
# انقر مرتين على الملف
setup-remote-agent.bat
```

### 2️⃣ أو التشغيل اليدوي
```bash
# تثبيت المتطلبات
npm run setup

# إعداد قاعدة البيانات
npm run setup:db

# تشغيل النظام
npm run dev
```

### 3️⃣ اختبار النظام
```bash
# اختبار شامل
npm test

# أو استخدام ملف الاختبار
run-tests.bat
```

---

## 🌐 الوصول السريع

| الخدمة | الرابط | الوصف |
|--------|--------|-------|
| **واجهة العميل** | http://localhost:5173 | لوحة التحكم الرئيسية |
| **API الخادم** | http://localhost:8081 | خادم API المحلي |
| **API الوكلاء** | http://localhost:8081/api/external/ | APIs الوكلاء الخارجية |
| **فحص الحالة** | http://localhost:8081/api/external/health | فحص حالة الخادم |

---

## 🔑 بيانات الدخول

```
Username: admin
Password: admin123456
```

---

## 🧪 اختبار سريع

### اختبار API الوكلاء:
```bash
curl -X POST http://localhost:8081/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "test123", "clientCode": 1004, "clientToken": "TEST1004"}'
```

### أوامر الاختبار المتاحة:
```bash
npm test              # اختبار شامل
npm run test:server   # اختبار الخادم
npm run test:api      # اختبار API
npm run test:db       # اختبار قاعدة البيانات
```

---

## 🛠️ حل المشاكل السريع

### المشكلة: الخادم لا يعمل
```bash
# تحقق من Node.js
node --version

# تحقق من المنفذ
netstat -an | findstr :8081
```

### المشكلة: قاعدة البيانات لا تعمل
```bash
# تحقق من PostgreSQL
psql --version

# إنشاء قاعدة البيانات يدوياً
psql -U postgres -c "CREATE DATABASE yemclient_db;"
```

### المشكلة: API لا يستجيب
```bash
# فحص حالة الخادم
curl http://localhost:8081/api/external/health

# تحقق من السجلات
# راجع نافذة الخادم للأخطاء
```

---

## 📁 الملفات المهمة

| الملف | الوصف |
|-------|-------|
| `setup-remote-agent.bat` | إعداد وتشغيل تلقائي |
| `run-tests.bat` | تشغيل الاختبارات |
| `test-remote-agent.js` | اختبار شامل للنظام |
| `دليل-تشغيل-Remote-Agent.md` | دليل مفصل |
| `server/.env` | إعدادات الخادم |

---

## 🆘 المساعدة السريعة

### إذا لم يعمل شيء:
1. تأكد من تثبيت Node.js و PostgreSQL
2. شغل `setup-remote-agent.bat`
3. انتظر اكتمال التثبيت
4. شغل `npm test` للتحقق

### للمساعدة المفصلة:
- راجع `دليل-تشغيل-Remote-Agent.md`
- راجع `TROUBLESHOOTING.md`
- تحقق من سجلات الخادم

---

## ✨ نصائح سريعة

- استخدم `Ctrl+C` لإيقاف الخدمات
- راجع نافذة الخادم للأخطاء
- استخدم `npm run dev` لتشغيل النظام كاملاً
- استخدم `npm test` للتحقق من عمل النظام

---

**🎯 الهدف:** تشغيل خدمة Remote Agent في أقل من 5 دقائق!
