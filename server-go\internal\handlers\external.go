package handlers

import (
	"encoding/json"
	"net/http"
	"time"
	
	"yemclient-server/internal/database"
)

func ExternalHealthHandler(w http.ResponseWriter, r *http.Request) {
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "healthy",
		"version": "1.0.0",
	})
}

func ExternalStatsHandler(w http.ResponseWriter, r *http.Request) {
	stats := map[string]interface{}{
		"uptime":      "24h",
		"totalUsers":  4,
		"totalClients": 6,
		"totalAgents": 5,
		"timestamp":   time.Now().Format(time.RFC3339),
	}
	json.NewEncoder(w).Encode(stats)
}

func HealthHandler(w http.ResponseWriter, r *http.Request) {
	// Check database connection
	if sqlDB, err := database.DB.DB(); err == nil {
		if err := sqlDB.Ping(); err != nil {
			http.Error(w, "Database connection failed", http.StatusServiceUnavailable)
			return
		}
	}
	
	json.NewEncoder(w).Encode(map[string]string{
		"status": "ok",
	})
}
