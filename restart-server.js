// إعادة تشغيل الخادم الصحيح
const { exec } = require('child_process');
const path = require('path');

console.log('🔄 إعادة تشغيل الخادم...');

// إيقاف جميع عمليات Node.js
console.log('⏹️ إيقاف الخوادم الحالية...');
exec('taskkill /f /im node.exe', (error, stdout, stderr) => {
  if (error && !error.message.includes('not found')) {
    console.log('⚠️ خطأ في إيقاف العمليات:', error.message);
  } else {
    console.log('✅ تم إيقاف العمليات السابقة');
  }
  
  // انتظار ثانيتين ثم تشغيل الخادم الجديد
  setTimeout(() => {
    console.log('🚀 تشغيل working-server.js...');
    
    const serverProcess = exec('node working-server.js', {
      cwd: path.join(__dirname, 'server')
    });
    
    serverProcess.stdout.on('data', (data) => {
      console.log('📤 Server:', data.toString().trim());
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error('❌ Server Error:', data.toString().trim());
    });
    
    serverProcess.on('close', (code) => {
      console.log(`🛑 Server process exited with code ${code}`);
    });
    
    // إبقاء العملية حية
    process.stdin.resume();
    
  }, 2000);
});

// التعامل مع إيقاف العملية
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  process.exit(0);
});
