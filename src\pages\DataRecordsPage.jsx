import React, { useState } from 'react'
import {
  Box,
  Typography,
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Tooltip,
  Avatar
} from '@mui/material'
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useAuth } from '../contexts/AuthContext'
import { useSnackbar } from 'notistack'
import DataRecordForm from '../components/forms/DataRecordForm'
import DataRecordDetailsDialog from '../components/dialogs/DataRecordDetailsDialog'
import EnhancedButton from '../components/common/EnhancedButton'

const DataRecordsPage = () => {
  const { api, hasPermission } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const queryClient = useQueryClient()

  // حالات الصفحة
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [dateRange, setDateRange] = useState([null, null])
  const [dialogOpen, setDialogOpen] = useState(false)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState(null)
  const [recordToDelete, setRecordToDelete] = useState(null)

  // جلب بيانات سجلات البيانات من API
  const { data, isLoading, refetch } = useQuery(
    ['dataRecords', page, pageSize, searchTerm, typeFilter, dateRange],
    () => api.get('/api/data-records', {
      params: {
        page: page + 1,
        limit: pageSize,
        search: searchTerm,
        status: typeFilter,
        startDate: dateRange?.[0]?.toISOString(),
        endDate: dateRange?.[1]?.toISOString()
      }
    }).then(res => res.data),
    {
      keepPreviousData: true,
      staleTime: 30000,
      onError: (error) => {
        console.error('Error fetching data records:', error)
        enqueueSnackbar('خطأ في جلب سجلات البيانات', { variant: 'error' })
      }
    }
  )

  // حذف سجل
  const deleteMutation = useMutation(
    (recordId) => api.delete(`/api/data-records/${recordId}`),
    {
      onSuccess: () => {
        enqueueSnackbar('تم حذف السجل بنجاح', { variant: 'success' })
        queryClient.invalidateQueries('dataRecords')
        setDeleteDialogOpen(false)
        setRecordToDelete(null)
      },
      onError: (error) => {
        console.error('Error deleting record:', error)
        enqueueSnackbar('خطأ في حذف السجل', { variant: 'error' })
      }
    }
  )

  // تعريف الأعمدة
  const columns = [
    {
      field: 'avatar',
      headerName: '',
      width: 60,
      sortable: false,
      renderCell: (params) => (
        <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
          📊
        </Avatar>
      )
    },
    {
      field: 'id',
      headerName: 'المعرف',
      width: 80
    },
    {
      field: 'agentId',
      headerName: 'رقم الوكيل',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value || 'غير محدد'}
          color="primary"
          variant="outlined"
          size="small"
          sx={{
            fontWeight: 'bold',
            backgroundColor: params.value ? '#e8f5e8' : '#ffebee'
          }}
        />
      )
    },
    {
      field: 'agentReference',
      headerName: 'مرجع الوكيل',
      width: 130,
      renderCell: (params) => (
        <Chip
          label={params.value || 'غير محدد'}
          color="secondary"
          variant="outlined"
          size="small"
          sx={{
            fontWeight: 'bold',
            backgroundColor: params.value ? '#e3f2fd' : '#ffebee'
          }}
        />
      )
    },
    {
      field: 'clientCode',
      headerName: 'رمز العميل',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color="info"
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'operationDate',
      headerName: 'تاريخ العملية',
      width: 180,
      renderCell: (params) => (
        new Date(params.value).toLocaleString('ar-EG', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      )
    },
    {
      field: 'operationStatus',
      headerName: 'حالة العملية',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value === 1 ? 'ناجحة' : 'فاشلة'}
          color={params.value === 1 ? 'success' : 'error'}
          size="small"
        />
      )
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Tooltip title="عرض التفاصيل" arrow>
            <IconButton
              size="small"
              onClick={() => handleViewDetails(params.row)}
              sx={{
                color: 'info.main',
                backgroundColor: 'info.light',
                '&:hover': { backgroundColor: 'info.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <ViewIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          {hasPermission('dataRecords', 'update') && (
            <Tooltip title="تعديل" arrow>
              <IconButton
                size="small"
                onClick={() => handleEdit(params.row)}
                sx={{
                  color: 'warning.main',
                  backgroundColor: 'warning.light',
                  '&:hover': { backgroundColor: 'warning.main', color: 'white' },
                  borderRadius: '8px',
                  padding: '8px'
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          {hasPermission('dataRecords', 'delete') && (
            <Tooltip title="حذف" arrow>
              <IconButton
                size="small"
                onClick={() => handleDelete(params.row)}
                  sx={{
                    color: 'error.main',
                    backgroundColor: 'error.light',
                    '&:hover': { backgroundColor: 'error.main', color: 'white' },
                    borderRadius: '8px',
                    padding: '8px'
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
          )}
        </Box>
      )
    }
  ]

  // معالجات الأحداث
  const handleViewDetails = (record) => {
    setSelectedRecord(record)
    setDetailsDialogOpen(true)
  }



  const handleEdit = (record) => {
    setSelectedRecord(record)
    setDialogOpen(true)
  }

  const handleDelete = (record) => {
    setRecordToDelete(record)
    setDeleteDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setSelectedRecord(null)
  }

  const handleCloseDetailsDialog = () => {
    setDetailsDialogOpen(false)
    setSelectedRecord(null)
  }

  const handleConfirmDelete = () => {
    if (recordToDelete) {
      deleteMutation.mutate(recordToDelete.id)
    }
  }

  return (
    <Box sx={{
      direction: 'rtl',
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* شريط البحث - يملأ العرض */}
      <Box sx={{
        mb: 1,
        direction: 'rtl',
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        width: '100%'
      }}>
        <TextField
          placeholder="البحث في سجلات البيانات..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
          }}
          sx={{
            direction: 'rtl',
            flexGrow: 1,
            maxWidth: '400px',
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'white',
              borderRadius: 1
            }
          }}
          size="small"
        />
        <Typography variant="body2" color="text.secondary" sx={{ flexShrink: 0 }}>
          📊 سجلات البيانات تتولد تلقائياً من عمليات API
        </Typography>
      </Box>

      {/* جدول البيانات - يملأ المساحة المتبقية */}
      <Box sx={{
        flexGrow: 1,
        width: '100%',
        direction: 'rtl',
        backgroundColor: 'white',
        borderRadius: 1,
        overflow: 'hidden',
        minHeight: 0
      }}>
        <DataGrid
          rows={data?.dataRecords || []}
          columns={columns}
          pageSize={pageSize}
          onPageSizeChange={setPageSize}
          rowsPerPageOptions={[5, 10, 25, 50]}
          page={page}
          onPageChange={setPage}
          rowCount={data?.pagination?.total || 0}
          paginationMode="server"
          loading={isLoading}
          disableSelectionOnClick
          sx={{
            direction: 'rtl',
            border: 'none',
            height: '100%',
            width: '100%',
            '& .MuiDataGrid-main': {
              direction: 'rtl'
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: '#f5f5f5',
              borderBottom: '1px solid #e0e0e0'
            },
            '& .MuiDataGrid-cell': {
              borderBottom: '1px solid #f0f0f0',
              textAlign: 'right'
            },
            '& .MuiDataGrid-row:hover': {
              backgroundColor: '#f9f9f9'
            }
          }}
          localeText={{
            noRowsLabel: 'لا توجد سجلات بيانات',
            footerRowSelected: (count) => `${count} صف محدد`,
            footerTotalRows: 'إجمالي الصفوف:',
            footerPaginationRowsPerPage: 'صفوف لكل صفحة:',
          }}
        />
      </Box>

      {/* Dialog للتعديل */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            تعديل سجل البيانات
          </Typography>
          <IconButton onClick={handleCloseDialog} size="small">
            <span style={{ fontSize: '20px' }}>✕</span>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <DataRecordForm
            record={selectedRecord}
            onSuccess={() => {
              queryClient.invalidateQueries('dataRecords')
              handleCloseDialog()
            }}
            readOnly={!hasPermission('security', 'manage')}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog تفاصيل السجل */}
      <DataRecordDetailsDialog
        open={detailsDialogOpen}
        onClose={handleCloseDetailsDialog}
        record={selectedRecord}
      />

      {/* Dialog تأكيد الحذف */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">تأكيد الحذف</Typography>
          <IconButton onClick={() => setDeleteDialogOpen(false)} size="small">
            <span style={{ fontSize: '20px' }}>✕</span>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف سجل البيانات رقم "{recordToDelete?.id}"؟
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>إلغاء</Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            disabled={deleteMutation.isLoading}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default DataRecordsPage
