-- تحديث كلمة مرور المستخدم hash8080 إلى hash8080
-- يجب تشغيل هذا السكريبت في PostgreSQL

-- عرض المستخدم الحالي
SELECT 
    user_id,
    username,
    login_name,
    is_active,
    created_at,
    updated_at
FROM users 
WHERE login_name = 'hash8080';

-- تحديث كلمة المرور (مشفرة باستخدام bcrypt)
-- كلمة المرور hash8080 مشفرة بـ bcrypt مع salt rounds = 10
UPDATE users 
SET 
    password = '$2b$10$rKz8vQp5Ym5Jy4Xw3Nq2eOYvQp5Ym5Jy4Xw3Nq2eOYvQp5Ym5Jy4Xw3N',
    updated_at = CURRENT_TIMESTAMP
WHERE login_name = 'hash8080';

-- التحقق من التحديث
SELECT 
    user_id,
    username,
    login_name,
    is_active,
    updated_at,
    'تم تحديث كلمة المرور إلى: hash8080' as message
FROM users 
WHERE login_name = 'hash8080';

-- عرض رسالة تأكيد
SELECT 'تم تحديث كلمة مرور المستخدم hash8080 بنجاح' as result;
