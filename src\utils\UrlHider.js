/**
 * نظام إخفاء URL وجعله ثابت
 */

class UrlHider {
  constructor() {
    this.originalUrl = window.location.href;
    this.baseUrl = window.location.origin + window.location.pathname;
    this.isActive = false;
  }

  /**
   * تفعيل إخفاء URL
   */
  activate() {
    if (this.isActive) return;

    this.isActive = true;

    // إخفاء hash من URL
    this.hideHash();

    // منع تغيير URL
    this.preventUrlChange();

    // مراقبة تغييرات URL
    this.monitorUrlChanges();

    console.log('🔒 URL Hider activated - URL is now static');
  }

  /**
   * إلغاء تفعيل إخفاء URL
   */
  deactivate() {
    if (!this.isActive) return;

    this.isActive = false;

    // إزالة event listeners
    window.removeEventListener('popstate', this.handlePopState);
    window.removeEventListener('hashchange', this.handleHashChange);

    console.log('🔓 URL Hider deactivated');
  }

  /**
   * إخفاء hash من URL
   */
  hideHash() {
    // إزالة hash من URL بدون إعادة تحميل الصفحة
    if (window.location.hash) {
      const newUrl = window.location.href.split('#')[0];
      window.history.replaceState(null, '', newUrl);
    }
  }

  /**
   * منع تغيير URL
   */
  preventUrlChange() {
    // منع popstate (زر الرجوع/التقدم)
    this.handlePopState = (event) => {
      event.preventDefault();
      window.history.replaceState(null, '', this.baseUrl);
      return false;
    };

    // منع hashchange
    this.handleHashChange = (event) => {
      event.preventDefault();
      window.history.replaceState(null, '', this.baseUrl);
      return false;
    };

    window.addEventListener('popstate', this.handlePopState);
    window.addEventListener('hashchange', this.handleHashChange);
  }

  /**
   * مراقبة تغييرات URL
   */
  monitorUrlChanges() {
    // مراقبة دورية للتأكد من ثبات URL
    this.urlMonitor = setInterval(() => {
      if (window.location.href !== this.baseUrl) {
        window.history.replaceState(null, '', this.baseUrl);
      }
    }, 100);
  }

  /**
   * إعادة تعيين URL للحالة الأساسية
   */
  resetUrl() {
    window.history.replaceState(null, '', this.baseUrl);
  }

  /**
   * الحصول على URL الأساسي
   */
  getBaseUrl() {
    return this.baseUrl;
  }
}

// إنشاء instance واحد
const urlHider = new UrlHider();

/**
 * Hook لاستخدام URL Hider
 */
export function useUrlHider() {
  const activate = () => urlHider.activate();
  const deactivate = () => urlHider.deactivate();
  const resetUrl = () => urlHider.resetUrl();
  const getBaseUrl = () => urlHider.getBaseUrl();

  return {
    activate,
    deactivate,
    resetUrl,
    getBaseUrl,
    isActive: urlHider.isActive
  };
}

/**
 * تفعيل تلقائي عند تحميل الصفحة
 */
export function initializeUrlHider() {
  // انتظار تحميل DOM
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      urlHider.activate();
    });
  } else {
    urlHider.activate();
  }
}

/**
 * مكون React لتفعيل URL Hider
 */
export function UrlHiderProvider({ children }) {
  if (typeof React !== 'undefined') {
    React.useEffect(() => {
      urlHider.activate();

      return () => {
        urlHider.deactivate();
      };
    }, []);
  }

  return children;
}

export default urlHider;
