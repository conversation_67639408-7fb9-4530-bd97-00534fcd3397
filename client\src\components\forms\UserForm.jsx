import React, { useEffect } from 'react'
import {
  Box,
  TextField,
  Button,
  Grid,
  Typography,
  Divider,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material'
import { ExpandMore } from '@mui/icons-material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useMutation } from 'react-query'
import { useAuth } from '../../contexts/AuthContext'
import { useSnackbar } from 'notistack'

const createSchema = (isEdit = false) => yup.object({
  username: yup.string().required('اسم المستخدم مطلوب'),
  loginName: yup.string()
    .required('اسم الدخول مطلوب')
    .min(8, 'اسم الدخول يجب أن يكون 8 أحرف على الأقل'),
  password: yup.string().required('كلمة المرور مطلوبة').min(8, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'),
  deviceId: yup.string().nullable(),
  device1: yup.string().nullable(),
  isActive: yup.boolean(),
  permissions: yup.object({
    isAdmin: yup.boolean(),
    clients: yup.object({
      create: yup.boolean(),
      read: yup.boolean(),
      update: yup.boolean(),
      delete: yup.boolean()
    }),
    agents: yup.object({
      create: yup.boolean(),
      read: yup.boolean(),
      update: yup.boolean(),
      delete: yup.boolean()
    }),
    users: yup.object({
      create: yup.boolean(),
      read: yup.boolean(),
      update: yup.boolean(),
      delete: yup.boolean()
    }),
    dashboard: yup.object({
      read: yup.boolean()
    }),
    security: yup.object({
      read: yup.boolean(),
      manage: yup.boolean()
    })
  })
})

const UserForm = ({ user, onSuccess, readOnly = false }) => {
  const { api } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  const isEdit = !!user

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(createSchema(isEdit)),
    defaultValues: {
      username: '',
      loginName: '',
      password: '',
      deviceId: '',
      device1: '',
      isActive: true,
      permissions: {
        isAdmin: false,
        clients: {
          create: false,
          read: false,
          update: false,
          delete: false
        },
        agents: {
          create: false,
          read: false,
          update: false,
          delete: false
        },
        users: {
          create: false,
          read: false,
          update: false,
          delete: false
        },
        dashboard: {
          read: false
        },
        security: {
          read: false,
          manage: false
        }
      }
    }
  })

  const watchPermissions = watch('permissions')

  // إضافة/تحديث مستخدم
  const mutation = useMutation(
    (data) => {
      const submitData = { ...data }
      if (user && !submitData.password) {
        delete submitData.password
      }

      if (user) {
        return api.put(`/api/users/${user.id}`, submitData)
      } else {
        return api.post('/api/users', submitData)
      }
    },
    {
      onSuccess: () => {
        enqueueSnackbar(
          user ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح',
          { variant: 'success' }
        )
        onSuccess()
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.error || 'حدث خطأ في حفظ البيانات',
          { variant: 'error' }
        )
      }
    }
  )

  // تعبئة النموذج عند التعديل
  useEffect(() => {
    if (user) {
      reset({
        username: user.username || '',
        loginName: user.loginName || '',
        password: '',
        deviceId: user.deviceId || '',
        device1: user.device1 || '',
        isActive: user.isActive !== undefined ? user.isActive : true,
        permissions: user.permissions || {
          isAdmin: false,
          clients: { create: false, read: false, update: false, delete: false },
          agents: { create: false, read: false, update: false, delete: false },
          users: { create: false, read: false, update: false, delete: false },
          dashboard: { read: false },
          security: { read: false, manage: false }
        }
      })
    }
  }, [user, reset])

  const onSubmit = (data) => {
    mutation.mutate(data)
  }

  const PermissionSection = ({ title, resource, permissions, control, readOnly }) => (
    <Card variant="outlined" sx={{ mb: 2 }}>
      <CardContent>
        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
          {title}
        </Typography>
        <Grid container spacing={2}>
          {Object.entries(permissions).map(([action, label]) => (
            <Grid item xs={6} md={3} key={action}>
              <Controller
                name={`permissions.${resource}.${action}`}
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Switch
                        {...field}
                        checked={field.value || false}
                        disabled={readOnly}
                      />
                    }
                    label={label}
                  />
                )}
              />
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  )

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            معلومات المستخدم الأساسية
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="username"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="اسم المستخدم"
                error={!!errors.username}
                helperText={errors.username?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="loginName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="اسم الدخول (8 أحرف على الأقل)"
                error={!!errors.loginName}
                helperText={errors.loginName?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="password"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={user ? "كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)" : "كلمة المرور (8 أحرف على الأقل)"}
                type="password"
                error={!!errors.password}
                helperText={errors.password?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="deviceId"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="معرف الجهاز 1 (اختياري)"
                placeholder="device_abc123_1234567890"
                disabled={readOnly}
                helperText="سيتم تعيينه تلقائياً عند أول تسجيل دخول"
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="device1"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="معرف الجهاز 2 (اختياري)"
                placeholder="device_def456_0987654321"
                disabled={readOnly}
                helperText="معرف الجهاز الثاني للمستخدم"
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Controller
            name="isActive"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Switch
                    {...field}
                    checked={field.value}
                    disabled={readOnly}
                  />
                }
                label="المستخدم نشط"
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            الصلاحيات
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12}>
          <Controller
            name="permissions.isAdmin"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Switch
                    {...field}
                    checked={field.value || false}
                    disabled={readOnly}
                    color="error"
                  />
                }
                label="مدير النظام (كامل الصلاحيات)"
              />
            )}
          />
        </Grid>

        {!watchPermissions?.isAdmin && (
          <>
            <Grid item xs={12}>
              <PermissionSection
                title="صلاحيات العملاء"
                resource="clients"
                permissions={{
                  create: 'إضافة',
                  read: 'عرض',
                  update: 'تعديل',
                  delete: 'حذف'
                }}
                control={control}
                readOnly={readOnly}
              />
            </Grid>

            <Grid item xs={12}>
              <PermissionSection
                title="صلاحيات الوكلاء"
                resource="agents"
                permissions={{
                  create: 'إضافة',
                  read: 'عرض',
                  update: 'تعديل',
                  delete: 'حذف'
                }}
                control={control}
                readOnly={readOnly}
              />
            </Grid>

            <Grid item xs={12}>
              <PermissionSection
                title="صلاحيات المستخدمين"
                resource="users"
                permissions={{
                  create: 'إضافة',
                  read: 'عرض',
                  update: 'تعديل',
                  delete: 'حذف'
                }}
                control={control}
                readOnly={readOnly}
              />
            </Grid>

            <Grid item xs={12}>
              <PermissionSection
                title="صلاحيات البيانات"
                resource="dataRecords"
                permissions={{
                  create: 'إضافة',
                  read: 'عرض',
                  update: 'تعديل',
                  delete: 'حذف'
                }}
                control={control}
                readOnly={readOnly}
              />
            </Grid>

            <Grid item xs={12}>
              <PermissionSection
                title="صلاحيات أخرى"
                resource="dashboard"
                permissions={{
                  read: 'عرض لوحة التحكم'
                }}
                control={control}
                readOnly={readOnly}
              />
            </Grid>

            <Grid item xs={12}>
              <PermissionSection
                title="صلاحيات الأمان"
                resource="security"
                permissions={{
                  read: 'عرض تقارير الأمان',
                  manage: 'إدارة الأمان'
                }}
                control={control}
                readOnly={readOnly}
              />
            </Grid>
          </>
        )}

        {!readOnly && (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                disabled={mutation.isLoading}
              >
                {mutation.isLoading ? 'جاري الحفظ...' : (user ? 'تحديث' : 'إضافة')}
              </Button>
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  )
}

export default UserForm
