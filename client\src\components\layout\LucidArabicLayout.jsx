import React, { useState, useEffect } from 'react'
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip
} from '@mui/material'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import UserProfileDialog from '../dialogs/UserProfileDialog'

const drawerWidth = 300

// ألوان يمن موبايل - بسيطة وأنيقة
const YEMEN_COLORS = {
  primary: '#B40047',    // أحمر يمن موبايل
  secondary: '#FFFABD',  // ذهبي فاتح
  background: '#f8f9fa'  // خلفية فاتحة
}

const LucidArabicLayout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true) // حالة إظهار/إخفاء القائمة الجانبية
  const [anchorEl, setAnchorEl] = useState(null)
  const [profileDialogOpen, setProfileDialogOpen] = useState(false) // حالة نافذة الملف الشخصي
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout, hasPermission } = useAuth()

  // إجبار التحديث والأنماط
  useEffect(() => {
    console.log('🎨 تحميل تصميم Lucid العربي مع ألوان يمن موبايل')

    // إضافة أنماط CSS مباشرة للجسم
    document.body.style.direction = 'rtl'
    document.body.style.textAlign = 'right'
    document.documentElement.style.direction = 'rtl'
    document.body.style.backgroundColor = YEMEN_COLORS.background

    return () => {
      document.body.style.backgroundColor = ''
    }
  }, [])

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleProfileOpen = () => {
    setProfileDialogOpen(true)
    handleClose()
  }

  const handleLogout = () => {
    logout()
    handleClose()
  }

  // قوائم النظام مع ألوان يمن موبايل البسيطة
  const systemMenus = [
    {
      text: 'لوحة التحكم',
      icon: '📊',
      path: '/dashboard',
      permission: null
    },
    {
      text: 'العملاء',
      icon: '👥',
      path: '/clients',
      permission: { resource: 'clients', action: 'read' }
    },
    {
      text: 'الوكلاء',
      icon: '🏢',
      path: '/agents',
      permission: { resource: 'agents', action: 'read' }
    },
    {
      text: 'المستخدمين',
      icon: '👤',
      path: '/users',
      permission: { resource: 'users', action: 'read' }
    },
    {
      text: 'البيانات',
      icon: '📋',
      path: '/data-records',
      permission: null
    },
    {
      text: 'الأمان',
      icon: '🔒',
      path: '/security',
      permission: { resource: 'security', action: 'read' }
    }
  ]

  const getPageTitle = () => {
    const currentItem = systemMenus.find(item =>
      item.path === location.pathname
    )
    return currentItem?.text || 'لوحة التحكم'
  }

  // مكون القائمة الجانبية - التصميم الأصلي البسيط مع ألوان يمن موبايل
  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', direction: 'rtl' }}>
      {/* رأس القائمة - بسيط مع ألوان يمن موبايل */}
      <Box sx={{
        background: `linear-gradient(135deg, ${YEMEN_COLORS.primary} 0%, #8A0036 100%)`,
        color: 'white',
        p: 3,
        position: 'relative',
        overflow: 'hidden'
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 3,
          pb: 3,
          borderBottom: '1px solid rgba(255,255,255,0.2)',
          position: 'relative',
          zIndex: 1
        }}>
          <Avatar sx={{
            width: 64,
            height: 64,
            ml: 2,
            background: 'linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%)',
            fontSize: '28px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
            border: '2px solid rgba(255,255,255,0.2)'
          }}>
            👤
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="body2" sx={{
              opacity: 0.9,
              fontSize: '14px'
            }}>
              مرحباً،
            </Typography>
            <Typography variant="h6" sx={{
              fontWeight: 700,
              fontSize: '18px',
              textShadow: '0 2px 4px rgba(0,0,0,0.3)'
            }}>
              {user?.username || 'مستخدم'}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* قائمة التنقل البسيطة مع ألوان يمن موبايل */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', pt: 2, px: 1 }}>
        <List sx={{ py: 0 }}>
          {systemMenus.map((item) => {
            if (item.permission && !hasPermission(item.permission.resource, item.permission.action)) {
              return null
            }

            const isActive = location.pathname === item.path

            return (
              <ListItem key={item.path} disablePadding sx={{ mb: 1 }}>
                <ListItemButton
                  onClick={() => navigate(item.path)}
                  sx={{
                    borderRadius: 2,
                    py: 1.5,
                    px: 2,
                    background: isActive ? YEMEN_COLORS.primary : 'transparent',
                    color: isActive ? 'white' : '#333',
                    transition: 'all 0.3s ease',
                    direction: 'rtl',
                    '&:hover': {
                      background: isActive ? YEMEN_COLORS.primary : 'rgba(180, 0, 71, 0.1)',
                    }
                  }}
                >
                  <ListItemIcon sx={{
                    minWidth: 40,
                    marginLeft: '12px',
                    marginRight: 0,
                    color: isActive ? 'white' : YEMEN_COLORS.primary
                  }}>
                    <span style={{ fontSize: '20px' }}>
                      {item.icon}
                    </span>
                  </ListItemIcon>
                  <ListItemText
                    primary={item.text}
                    sx={{
                      textAlign: 'right',
                      '& .MuiListItemText-primary': {
                        fontWeight: isActive ? 600 : 500,
                        fontSize: '16px',
                        textAlign: 'right'
                      }
                    }}
                  />
                </ListItemButton>
              </ListItem>
            )
          })}
        </List>
      </Box>
      
      {/* تذييل القائمة الجانبية */}
      <Box sx={{ 
        py: 2, 
        px: 3, 
        textAlign: 'center',
        background: YEMEN_MOBILE_COLORS.light,
        borderTop: `1px solid ${YEMEN_MOBILE_COLORS.light}`
      }}>
        <Typography sx={{ 
          color: YEMEN_MOBILE_COLORS.primary,
          fontSize: '12px',
          fontFamily: 'Khalid-Art-bold, sans-serif'
        }}>
          © 2023 يمن موبايل. جميع الحقوق محفوظة.
        </Typography>
      </Box>
    </Box>
  )

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      direction: 'rtl',
      backgroundColor: YEMEN_COLORS.background,
      position: 'relative',
      width: '100vw',
      overflow: 'hidden'
    }}>
      {/* شريط علوي مع ألوان يمن موبايل */}
      <AppBar
        position="fixed"
        sx={{
          width: '100vw',
          right: 0,
          left: 0,
          background: `linear-gradient(135deg, ${YEMEN_COLORS.primary} 0%, #8A0036 100%)`,
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          direction: 'rtl'
        }}
      >
        <Toolbar sx={{ direction: 'rtl' }}>
          {/* زر إخفاء/إظهار القائمة الجانبية للشاشات الكبيرة */}
          <Tooltip title={sidebarOpen ? "إخفاء القائمة" : "إظهار القائمة"}>
            <IconButton
              color="inherit"
              aria-label="toggle sidebar"
              onClick={handleSidebarToggle}
              sx={{
                ml: 2,
                display: { xs: 'none', sm: 'block' },
                backgroundColor: 'rgba(255,255,255,0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.2)',
                }
              }}
            >
              <span style={{ fontSize: '24px' }}>
                {sidebarOpen ? '◀' : '▶'}
              </span>
            </IconButton>
          </Tooltip>

          {/* زر القائمة للموبايل */}
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ ml: 2, display: { sm: 'none' } }}
          >
            <span style={{ fontSize: '24px' }}>☰</span>
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{
            flexGrow: 1,
            fontWeight: 700,
            textAlign: 'right',
            fontSize: '20px',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)',
            letterSpacing: '0.5px'
          }}>
            {getPageTitle()}
          </Typography>

          <Tooltip title="الإشعارات">
            <IconButton size="large" color="inherit">
              <Badge badgeContent={0} color="error">
                <span style={{ fontSize: '24px' }}>🔔</span>
              </Badge>
            </IconButton>
          </Tooltip>

          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenu}
            color="inherit"
          >
            <span style={{ fontSize: '24px' }}>👤</span>
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            sx={{ direction: 'rtl' }}
          >
            <MenuItem onClick={handleProfileOpen} sx={{ direction: 'rtl', textAlign: 'right' }}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>👤</span>
              </ListItemIcon>
              الملف الشخصي
            </MenuItem>
            <MenuItem onClick={handleLogout} sx={{ direction: 'rtl', textAlign: 'right' }}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>🚪</span>
              </ListItemIcon>
              تسجيل الخروج
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* القائمة الجانبية على اليمين */}
      <Drawer
        variant="permanent"
        sx={{
          width: sidebarOpen ? drawerWidth : 0,
          flexShrink: 0,
          transition: 'width 0.3s ease-in-out',
          '& .MuiDrawer-paper': {
            width: sidebarOpen ? drawerWidth : 0,
            boxSizing: 'border-box',
            position: 'fixed',
            right: 0,
            left: 'auto',
            top: 0,
            height: '100vh',
            direction: 'rtl',
            zIndex: 1100,
            borderLeft: sidebarOpen ? '1px solid #e0e0e0' : 'none',
            borderRight: 'none',
            overflow: 'hidden',
            transition: 'width 0.3s ease-in-out'
          },
          display: { xs: 'none', sm: 'block' }
        }}
      >
        {sidebarOpen && drawer}
      </Drawer>

      {/* القائمة المؤقتة للموبايل */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            direction: 'rtl',

          },
        }}
      >
        {drawer}
      </Drawer>

      {/* المحتوى الرئيسي - يتوسع عند إخفاء القائمة */}
      <Box
        component="main"
        sx={{
          position: 'fixed',
          top: '64px',
          right: sidebarOpen ? `${drawerWidth}px` : '0px',
          left: 0,
          bottom: 0,
          backgroundColor: YEMEN_COLORS.background,
          direction: 'rtl',
          overflow: 'auto',
          width: sidebarOpen ? `calc(100vw - ${drawerWidth}px)` : '100vw',
          height: 'calc(100vh - 64px)',
          transition: 'right 0.3s ease-in-out, width 0.3s ease-in-out',
        }}
      >
        <Box sx={{
          p: 2,
          direction: 'rtl',
          width: '100%',
          height: '100%',
          minHeight: 'calc(100vh - 64px - 32px)'
        }}>
          {children}
        </Box>
      </Box>

      {/* نافذة الملف الشخصي */}
      <UserProfileDialog
        open={profileDialogOpen}
        onClose={() => setProfileDialogOpen(false)}
      />
    </Box>
  );
};

export default LucidArabicLayout;