import React from 'react'
import { IconButton, Tooltip } from '@mui/material'
import { ContentCopy } from '@mui/icons-material'
import { useSnackbar } from 'notistack'

/**
 * مكون زر النسخ مع دعم متعدد للمتصفحات
 */
const CopyButton = ({ 
  text, 
  tooltip = 'نسخ', 
  successMessage = 'تم النسخ بنجاح!',
  errorMessage = 'فشل في النسخ',
  size = 'small',
  sx = {}
}) => {
  const { enqueueSnackbar } = useSnackbar()

  const handleCopy = async () => {
    if (!text) {
      enqueueSnackbar('لا يوجد نص للنسخ', { variant: 'warning' })
      return
    }

    try {
      // الطريقة الحديثة - Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text)
        enqueueSnackbar(successMessage, { variant: 'success' })
        return
      }

      // الطريقة البديلة للمتصفحات القديمة أو HTTP
      await fallbackCopyTextToClipboard(text)
      enqueueSnackbar(successMessage, { variant: 'success' })
      
    } catch (error) {
      console.error('خطأ في النسخ:', error)
      enqueueSnackbar(errorMessage, { variant: 'error' })
    }
  }

  // دالة النسخ البديلة
  const fallbackCopyTextToClipboard = (text) => {
    return new Promise((resolve, reject) => {
      const textArea = document.createElement('textarea')
      textArea.value = text
      
      // جعل العنصر غير مرئي
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      textArea.style.opacity = '0'
      textArea.style.pointerEvents = 'none'
      textArea.style.zIndex = '-1'
      
      document.body.appendChild(textArea)
      
      try {
        textArea.focus()
        textArea.select()
        
        // للأجهزة المحمولة
        textArea.setSelectionRange(0, 99999)
        
        const successful = document.execCommand('copy')
        
        if (successful) {
          resolve()
        } else {
          reject(new Error('execCommand failed'))
        }
      } catch (err) {
        reject(err)
      } finally {
        document.body.removeChild(textArea)
      }
    })
  }

  return (
    <Tooltip title={tooltip} arrow>
      <IconButton
        size={size}
        onClick={handleCopy}
        sx={{
          color: 'primary.main',
          backgroundColor: 'primary.light',
          '&:hover': {
            backgroundColor: 'primary.main',
            color: 'white'
          },
          borderRadius: '6px',
          padding: '6px',
          ...sx
        }}
      >
        <ContentCopy fontSize="small" />
      </IconButton>
    </Tooltip>
  )
}

export default CopyButton
