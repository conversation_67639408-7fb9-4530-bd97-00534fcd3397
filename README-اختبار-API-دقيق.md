# 🎯 اختب<PERSON>ر API المطورين - دقيق ومضبوط

## 📋 البيانات المحددة للاختبار

### 👤 الوكيل التجريبي
- **الرقم**: 8 (أو أي وكيل بـ loginName = testuser)
- **اسم الدخول**: `testuser`
- **كلمة المرور**: `test123` (أو حسب قاعدة البيانات)

### 💼 العملاء للاختبار
- **العميل النشط**: `1000` (status = 1)
- **العميل غير النشط**: `1005` (status = 0)
- **العميل غير الموجود**: `9999` (غير موجود في قاعدة البيانات)

## 🚀 خطوات الاختبار

### 1. فحص البيانات
```bash
node check-specific-data.js
```
أو انقر مرتين على: `فحص-البيانات.bat`

### 2. تشغيل الخادم
```bash
cd server
node working-server.js
```
أو انقر مرتين على: `تشغيل-الخادم.bat`

### 3. اختبار API
```bash
node test-api-precise.js
```
أو انقر مرتين على: `اختبار-دقيق.bat`

### 4. تشغيل كامل (خادم + اختبار)
انقر مرتين على: `تشغيل-واختبار.bat`

## 🧪 سيناريوهات الاختبار

### ✅ الاختبار 1: وكيل صحيح + عميل نشط
```json
{
  "agent_login_name": "testuser",
  "agent_login_password": "test123",
  "client_code": 1000,
  "client_token": "ABC12345"
}
```
**النتيجة المتوقعة**: `status: "success"`

### ⚠️ الاختبار 2: وكيل صحيح + عميل غير نشط
```json
{
  "agent_login_name": "testuser",
  "agent_login_password": "test123",
  "client_code": 1005,
  "client_token": "TEST1005"
}
```
**النتيجة المتوقعة**: `status: "client_inactive"`

### ❌ الاختبار 3: وكيل صحيح + عميل غير موجود
```json
{
  "agent_login_name": "testuser",
  "agent_login_password": "test123",
  "client_code": 9999,
  "client_token": "DUMMY999"
}
```
**النتيجة المتوقعة**: `status: "client_error"`

### ❌ الاختبار 4: وكيل غير موجود
```json
{
  "agent_login_name": "nonexistent",
  "agent_login_password": "wrongpass",
  "client_code": 1000,
  "client_token": "ABC12345"
}
```
**النتيجة المتوقعة**: `status: "agent_error"`

### ❌ الاختبار 5: كلمة مرور خاطئة
```json
{
  "agent_login_name": "testuser",
  "agent_login_password": "wrongpassword",
  "client_code": 1000,
  "client_token": "ABC12345"
}
```
**النتيجة المتوقعة**: `status: "agent_error"`

## 🔧 استكشاف الأخطاء

### إذا لم يعمل API:
1. **تحقق من تشغيل الخادم**:
   - افتح المتصفح: `http://localhost:8080`
   - أو: `http://***********:8080`

2. **تحقق من البيانات**:
   - شغل: `node check-specific-data.js`
   - تأكد من وجود الوكيل `testuser`
   - تأكد من وجود العملاء 1000, 1005

3. **تحقق من كلمات المرور**:
   - كلمات المرور مشفرة بـ bcrypt
   - تحقق من قاعدة البيانات مباشرة

4. **تحقق من التوكنات**:
   - تأكد من صحة توكنات العملاء
   - API يدعم عدة طرق للتحقق من التوكن

## 🌐 الرابط النهائي للمطورين
```
POST http://***********:8080/api/external/verify-direct
```

## 📊 مراقبة السجلات
عند تشغيل الخادم، ستظهر سجلات مفصلة تشمل:
- 🎯 نوع السيناريو المكتشف
- 🔍 البارامترات المستلمة
- 👤 نتيجة البحث عن الوكيل
- 💼 نتيجة البحث عن العميل
- ✅/❌ نتيجة التحقق النهائية

## 🎯 الهدف
التأكد من أن آلية API تعمل بدقة ومضبوطة مع:
- ✅ التحقق الصحيح من الوكلاء
- ✅ التحقق الصحيح من العملاء
- ✅ معالجة جميع حالات الخطأ
- ✅ إرجاع استجابات واضحة ومفيدة
