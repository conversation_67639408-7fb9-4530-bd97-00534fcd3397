<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص الخادم</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            opacity: 0.9;
        }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 فحص حالة الخادم</h1>
        
        <div class="test-section">
            <div class="test-title">1. فحص الاتصال الأساسي</div>
            <button onclick="checkHealth()">فحص /health</button>
            <div id="healthResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. فحص APIs الخاصة بـ working-server.js</div>
            <button onclick="checkWorkingServerAPIs()">فحص APIs</button>
            <div id="apiResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. فحص خدمة الملفات الثابتة</div>
            <button onclick="checkStaticFiles()">فحص الملفات الثابتة</button>
            <div id="staticResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. فحص CORS</div>
            <button onclick="checkCORS()">فحص CORS</button>
            <div id="corsResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">5. تعليمات إعادة التشغيل</div>
            <div class="info">
                <strong>لتشغيل working-server.js الصحيح:</strong><br>
                1. انقر نقراً مزدوجاً على ملف <code>restart-working-server.bat</code><br>
                2. أو افتح Command Prompt وانتقل إلى مجلد المشروع واكتب:<br>
                <div class="code">
                    cd server<br>
                    node working-server.js
                </div>
            </div>
        </div>
    </div>

    <script>
        async function checkHealth() {
            const resultDiv = document.getElementById('healthResult');
            try {
                const response = await fetch('http://localhost:8080/health');
                const data = await response.json();
                resultDiv.innerHTML = `<div class="success">✅ الخادم يعمل: ${JSON.stringify(data)}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ خطأ في الاتصال: ${error.message}</div>`;
            }
        }
        
        async function checkWorkingServerAPIs() {
            const resultDiv = document.getElementById('apiResult');
            const apis = [
                '/api/dashboard/stats',
                '/api/users',
                '/api/clients',
                '/api/agents'
            ];
            
            let results = [];
            
            for (const api of apis) {
                try {
                    const response = await fetch(`http://localhost:8080${api}`);
                    if (response.status === 401) {
                        results.push(`✅ ${api}: يتطلب تسجيل دخول (طبيعي)`);
                    } else if (response.status === 404) {
                        results.push(`❌ ${api}: غير موجود (الخادم الخطأ)`);
                    } else {
                        results.push(`✅ ${api}: يعمل (${response.status})`);
                    }
                } catch (error) {
                    results.push(`❌ ${api}: خطأ - ${error.message}`);
                }
            }
            
            const isWorkingServer = results.some(r => r.includes('يتطلب تسجيل دخول'));
            const summary = isWorkingServer ? 
                '<div class="success">✅ يبدو أن working-server.js يعمل</div>' :
                '<div class="error">❌ الخادم الحالي ليس working-server.js</div>';
            
            resultDiv.innerHTML = summary + '<br>' + results.join('<br>');
        }
        
        async function checkStaticFiles() {
            const resultDiv = document.getElementById('staticResult');
            try {
                const response = await fetch('http://localhost:8080/');
                if (response.ok) {
                    const text = await response.text();
                    if (text.includes('نظام إدارة العملاء والوكلاء')) {
                        resultDiv.innerHTML = '<div class="success">✅ الملفات الثابتة تعمل بشكل صحيح</div>';
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ الملفات الثابتة لا تحتوي على المحتوى المتوقع</div>';
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ خطأ في تحميل الصفحة الرئيسية: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ خطأ في الوصول للملفات الثابتة: ${error.message}</div>`;
            }
        }
        
        async function checkCORS() {
            const resultDiv = document.getElementById('corsResult');
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        loginName: 'test',
                        password: 'test'
                    })
                });
                
                // إذا وصلنا هنا، فـ CORS يعمل
                resultDiv.innerHTML = '<div class="success">✅ CORS يعمل بشكل صحيح</div>';
            } catch (error) {
                if (error.message.includes('CORS')) {
                    resultDiv.innerHTML = '<div class="error">❌ مشكلة في CORS: ' + error.message + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="success">✅ CORS يعمل (خطأ آخر: ' + error.message + ')</div>';
                }
            }
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.onload = function() {
            checkHealth();
        };
    </script>
</body>
</html>
