package logger

import (
	"log"
	"net/http"
	"os"
	"time"
)

var (
	InfoLogger  *log.Logger
	ErrorLogger *log.Logger
)

func Init() {
	InfoLogger = log.New(os.<PERSON>dout, "INFO: ", log.Ldate|log.Ltime|log.Lshortfile)
	ErrorLogger = log.New(os.<PERSON>, "ERROR: ", log.Ldate|log.Ltime|log.Lshortfile)
}

func Info(v ...interface{}) {
	InfoLogger.Println(v...)
}

func Error(v ...interface{}) {
	ErrorLogger.Println(v...)
}

func LogRequest(r *http.Request) {
	InfoLogger.Printf("%s - %s %s %s", r.<PERSON>dd<PERSON>, r.Method, r.URL.Path, r.UserAgent())
}

func LogError(err error) {
	ErrorLogger.Printf("%v", err)
}

func LogDatabaseQuery(query string, duration time.Duration) {
	InfoLogger.Printf("DB Query: %s | Duration: %v", query, duration)
}
