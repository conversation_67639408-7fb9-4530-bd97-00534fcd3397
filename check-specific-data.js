// فحص البيانات المحددة للاختبار
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSpecificData() {
  try {
    console.log('🔍 فحص البيانات المحددة للاختبار...\n');

    // فحص الوكيل رقم 8
    console.log('👤 الوكيل رقم 8:');
    console.log('===============');
    const agent8 = await prisma.agent.findFirst({
      where: { id: 8 }
    });

    if (agent8) {
      console.log(`✅ الوكيل موجود:`);
      console.log(`   ID: ${agent8.id}`);
      console.log(`   اسم الدخول: ${agent8.loginName}`);
      console.log(`   اسم الوكيل: ${agent8.agentName}`);
      console.log(`   نشط: ${agent8.isActive ? 'نعم' : 'لا'}`);
      console.log(`   يوجد كلمة مرور: ${agent8.loginPassword ? 'نعم' : 'لا'}`);
    } else {
      console.log('❌ الوكيل رقم 8 غير موجود');
    }

    // فحص الوكيل بـ loginName = testuser
    console.log('\n👤 البحث عن testuser:');
    console.log('====================');
    const testUserAgent = await prisma.agent.findFirst({
      where: { loginName: 'testuser' }
    });

    if (testUserAgent) {
      console.log(`✅ الوكيل testuser موجود:`);
      console.log(`   ID: ${testUserAgent.id}`);
      console.log(`   اسم الوكيل: ${testUserAgent.agentName}`);
      console.log(`   نشط: ${testUserAgent.isActive ? 'نعم' : 'لا'}`);
      console.log(`   يوجد كلمة مرور: ${testUserAgent.loginPassword ? 'نعم' : 'لا'}`);
    } else {
      console.log('❌ الوكيل testuser غير موجود في جدول agents');
      
      // البحث في جدول users
      const testUser = await prisma.user.findFirst({
        where: { loginName: 'testuser' }
      });
      
      if (testUser) {
        console.log(`✅ المستخدم testuser موجود في جدول users:`);
        console.log(`   ID: ${testUser.id}`);
        console.log(`   اسم المستخدم: ${testUser.username}`);
        console.log(`   نشط: ${testUser.isActive ? 'نعم' : 'لا'}`);
        console.log(`   يوجد كلمة مرور: ${testUser.password ? 'نعم' : 'لا'}`);
      } else {
        console.log('❌ testuser غير موجود في أي جدول');
      }
    }

    // فحص العملاء المحددين
    console.log('\n💼 العملاء المحددين:');
    console.log('==================');
    
    const clientCodes = [1000, 1005, 9999];
    
    for (const code of clientCodes) {
      const client = await prisma.client.findFirst({
        where: { clientCode: code }
      });
      
      if (client) {
        console.log(`✅ العميل ${code}:`);
        console.log(`   الاسم: ${client.clientName}`);
        console.log(`   التوكن: ${client.token || 'غير محدد'}`);
        console.log(`   الحالة: ${client.status === 1 ? 'نشط' : 'غير نشط'}`);
        console.log(`   يوجد كلمة مرور: ${client.password ? 'نعم' : 'لا'}`);
      } else {
        console.log(`❌ العميل ${code}: غير موجود`);
      }
      console.log('');
    }

    // إنشاء بيانات الاختبار المطلوبة
    console.log('🧪 بيانات الاختبار المقترحة:');
    console.log('============================');
    
    const finalAgent = testUserAgent || agent8;
    if (finalAgent) {
      console.log('🔐 بيانات الوكيل للاختبار:');
      console.log(`   agent_login_name: ${finalAgent.loginName}`);
      console.log(`   agent_login_password: [تحتاج لمعرفة كلمة المرور الصحيحة]`);
    }
    
    console.log('\n💼 بيانات العملاء للاختبار:');
    console.log('   العميل النشط: 1000');
    console.log('   العميل غير النشط: 1005');
    console.log('   العميل غير الموجود: 9999');

  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSpecificData();
