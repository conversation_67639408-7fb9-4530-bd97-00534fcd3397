// تحديث الملفات المبنية مباشرة
const fs = require('fs');
const path = require('path');

console.log('🔄 تحديث الملفات المبنية...');

// البحث عن ملفات JavaScript في مجلد dist/assets
const assetsDir = path.join(__dirname, 'client', 'dist', 'assets');
const files = fs.readdirSync(assetsDir);

// البحث عن الملف الرئيسي
const mainJSFile = files.find(file => file.startsWith('index-') && file.endsWith('.js'));

if (!mainJSFile) {
    console.log('❌ لم يتم العثور على الملف الرئيسي');
    process.exit(1);
}

const mainJSPath = path.join(assetsDir, mainJSFile);
console.log(`📁 الملف الرئيسي: ${mainJSFile}`);

// قراءة محتوى الملف
let content = fs.readFileSync(mainJSPath, 'utf8');

// البحث عن النص القديم وتحديثه
const oldText1 = '"معرف الجهاز (اختياري)"';
const newText1 = '"معرف الجهاز 1 (اختياري)"';

const oldText2 = 'placeholder:"device_abc123_1234567890"';
const newText2 = 'placeholder:"device_abc123_1234567890"';

// إضافة الحقل الثاني
const deviceFieldPattern = /name:"deviceId"[^}]+}/g;
const matches = content.match(deviceFieldPattern);

if (matches && matches.length > 0) {
    console.log('✅ تم العثور على حقل معرف الجهاز');
    
    // تحديث التسمية
    content = content.replace(oldText1, newText1);
    
    // إضافة الحقل الثاني بعد الحقل الأول
    const firstDeviceField = matches[0];
    const secondDeviceField = firstDeviceField
        .replace('name:"deviceId"', 'name:"device1"')
        .replace('"معرف الجهاز 1 (اختياري)"', '"معرف الجهاز 2 (اختياري)"')
        .replace('device_abc123_1234567890', 'device_def456_0987654321')
        .replace('سيتم تعيينه تلقائياً عند أول تسجيل دخول', 'معرف الجهاز الثاني للمستخدم');
    
    // إدراج الحقل الثاني
    const insertionPoint = content.indexOf(firstDeviceField) + firstDeviceField.length;
    content = content.slice(0, insertionPoint) + ',' + secondDeviceField + content.slice(insertionPoint);
    
    console.log('✅ تم إضافة حقل معرف الجهاز 2');
} else {
    console.log('❌ لم يتم العثور على حقل معرف الجهاز');
}

// كتابة الملف المحدث
fs.writeFileSync(mainJSPath, content);
console.log('✅ تم تحديث الملف بنجاح!');

// إنشاء ملف تأكيد
const confirmationFile = path.join(__dirname, 'client', 'dist', 'update-confirmation.js');
const confirmationContent = `
// تأكيد التحديث
console.log('🎉 تم تحديث حقول معرف الجهاز بنجاح!');
console.log('📅 وقت التحديث: ${new Date().toLocaleString('ar-EG')}');

// إضافة تنبيه للمستخدم
setTimeout(() => {
    if (window.location.pathname.includes('users')) {
        const notification = document.createElement('div');
        notification.style.cssText = \`
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 9999;
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        \`;
        notification.innerHTML = \`
            <div style="font-weight: bold;">✅ تم التحديث!</div>
            <div style="font-size: 14px; margin-top: 5px;">حقل "معرف الجهاز 2" متاح الآن</div>
        \`;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}, 2000);
`;

fs.writeFileSync(confirmationFile, confirmationContent);

console.log('🎉 تم التحديث بنجاح!');
console.log('📋 الآن يمكنك:');
console.log('   1. الانتقال إلى صفحة المستخدمين');
console.log('   2. النقر على "إضافة مستخدم" أو تعديل مستخدم موجود');
console.log('   3. ستجد حقلين: "معرف الجهاز 1" و "معرف الجهاز 2"');
