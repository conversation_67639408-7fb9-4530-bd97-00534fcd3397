import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Avatar
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  Search,
  Visibility,
  AdminPanelSettings,
  Person
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useAuth } from '../contexts/AuthContext'
import { useSnackbar } from 'notistack'
import UserForm from '../components/forms/UserForm'
import EnhancedButton from '../components/common/EnhancedButton'
import PageContent from '../components/common/PageContent'

const UsersPage = () => {
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [selectedUser, setSelectedUser] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState(null)
  const [viewMode, setViewMode] = useState(false) // true للمشاهدة، false للتعديل

  // جلب بيانات المستخدمين من API
  const { data, isLoading, refetch } = useQuery(
    ['users', page, pageSize, searchTerm, sortField, sortOrder],
    () => api.get('/api/users', {
      params: {
        page: page + 1,
        limit: pageSize,
        search: searchTerm,
        sortField,
        sortOrder
      }
    }).then(res => res.data),
    {
      keepPreviousData: true,
      staleTime: 30000,
      onError: (error) => {
        console.error('Error fetching users:', error)
        enqueueSnackbar('خطأ في جلب بيانات المستخدمين', { variant: 'error' })
      }
    }
  )

  const { hasPermission, user: currentUser, api } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const queryClient = useQueryClient()

  // حذف مستخدم
  const deleteMutation = useMutation(
    (userId) => api.delete(`/api/users/${userId}`),
    {
      onSuccess: () => {
        enqueueSnackbar('تم حذف المستخدم بنجاح', { variant: 'success' })
        queryClient.invalidateQueries('users')
        setDeleteDialogOpen(false)
        setUserToDelete(null)
      },
      onError: (error) => {
        console.error('Error deleting user:', error)
        enqueueSnackbar('خطأ في حذف المستخدم', { variant: 'error' })
      }
    }
  )

  const columns = [
    {
      field: 'avatar',
      headerName: '',
      width: 60,
      sortable: false,
      renderCell: (params) => (
        <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
          {params.row.username?.charAt(0)}
        </Avatar>
      )
    },
    {
      field: 'id',
      headerName: 'المعرف',
      width: 80
    },
    {
      field: 'username',
      headerName: 'اسم المستخدم',
      width: 200
    },
    {
      field: 'loginName',
      headerName: 'اسم الدخول',
      width: 150
    },
    {
      field: 'permissions',
      headerName: 'النوع',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value?.isAdmin ? 'مدير' : 'مستخدم'}
          color={params.value?.isAdmin ? 'error' : 'primary'}
          size="small"
          icon={params.value?.isAdmin ? <AdminPanelSettings /> : <Person />}
        />
      )
    },
    {
      field: 'isActive',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      )
    },
    {
      field: '_count',
      headerName: 'عدد العملاء',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value?.clients || 0}
          color="info"
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'deviceId',
      headerName: 'الجهاز',
      width: 120,
      renderCell: (params) => (
        params.value ? (
          <Chip label="مرتبط" color="success" size="small" />
        ) : (
          <Chip label="غير مرتبط" color="default" size="small" />
        )
      )
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Tooltip title="عرض" arrow>
            <IconButton
              size="small"
              onClick={() => handleView(params.row)}
              sx={{
                color: 'primary.main',
                backgroundColor: 'primary.light',
                '&:hover': { backgroundColor: 'primary.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <span style={{ fontSize: '18px' }}>👁️</span>
            </IconButton>
          </Tooltip>
          <Tooltip title="تعديل" arrow>
            <IconButton
              size="small"
              onClick={() => handleEdit(params.row)}
              sx={{
                color: 'warning.main',
                backgroundColor: 'warning.light',
                '&:hover': { backgroundColor: 'warning.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <span style={{ fontSize: '18px' }}>✏️</span>
            </IconButton>
          </Tooltip>
          {params.row.id !== currentUser?.id && (
            <Tooltip title="حذف" arrow>
              <IconButton
                size="small"
                onClick={() => handleDelete(params.row)}
                sx={{
                  color: 'error.main',
                  backgroundColor: 'error.light',
                  '&:hover': { backgroundColor: 'error.main', color: 'white' },
                  borderRadius: '8px',
                  padding: '8px'
                }}
              >
                <span style={{ fontSize: '18px' }}>🗑️</span>
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )
    }
  ]

  const handleView = (user) => {
    setSelectedUser(user)
    setViewMode(true) // وضع المشاهدة فقط
    setDialogOpen(true)
  }

  const handleEdit = (user) => {
    setSelectedUser(user)
    setViewMode(false) // وضع التعديل
    setDialogOpen(true)
  }

  const handleDelete = (user) => {
    if (user.id === currentUser?.id) {
      enqueueSnackbar('لا يمكنك حذف حسابك الخاص', { variant: 'warning' })
      return
    }
    setUserToDelete(user)
    setDeleteDialogOpen(true)
  }

  const handleAdd = () => {
    setSelectedUser(null)
    setViewMode(false) // وضع الإضافة/التعديل
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setSelectedUser(null)
    setViewMode(false)
  }

  const handleConfirmDelete = () => {
    if (userToDelete) {
      deleteMutation.mutate(userToDelete.id)
    }
  }

  const handleResetDevice = (user) => {
    if (window.confirm(`هل أنت متأكد من إعادة تعيين الجهاز للمستخدم "${user.username}"؟\n\nسيتمكن المستخدم من تسجيل الدخول من أي جهاز جديد.`)) {
      api.post(`/auth/reset-device/${user.id}`, {})
        .then(() => {
          enqueueSnackbar('تم إعادة تعيين الجهاز بنجاح', { variant: 'success' })
          queryClient.invalidateQueries('users')
        })
        .catch((error) => {
          enqueueSnackbar(
            error.response?.data?.error || 'حدث خطأ أثناء إعادة تعيين الجهاز',
            { variant: 'error' }
          )
        })
    }
  }

  return (
    <Box sx={{
      direction: 'rtl',
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* شريط البحث والأدوات - يملأ العرض */}
      <Box sx={{
        mb: 1,
        direction: 'rtl',
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        width: '100%'
      }}>
        <TextField
          placeholder="البحث في المستخدمين..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <span style={{ marginRight: '8px', fontSize: '18px' }}>🔍</span>
          }}
          sx={{
            direction: 'rtl',
            flexGrow: 1,
            maxWidth: '400px',
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'white',
              borderRadius: 1
            }
          }}
          size="small"
        />
        <Button
          variant="contained"
          onClick={handleAdd}
          color="primary"
          sx={{
            minWidth: '180px',
            borderRadius: 1,
            flexShrink: 0
          }}
        >
          👤 إضافة مستخدم جديد
        </Button>
      </Box>

      {/* جدول المستخدمين - يملأ المساحة المتبقية */}
      <Box sx={{
        flexGrow: 1,
        width: '100%',
        direction: 'rtl',
        backgroundColor: 'white',
        borderRadius: 1,
        overflow: 'hidden',
        minHeight: 0
      }}>
        <DataGrid
          rows={data?.data || []}
          columns={columns}
          pageSize={pageSize}
          onPageSizeChange={setPageSize}
          rowsPerPageOptions={[5, 10, 25, 50]}
          page={page}
          onPageChange={setPage}
          rowCount={data?.total || 0}
          paginationMode="server"
          loading={isLoading}
          disableSelectionOnClick
          sx={{
            direction: 'rtl',
            border: 'none',
            height: '100%',
            width: '100%',
            '& .MuiDataGrid-main': {
              direction: 'rtl'
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: '#f5f5f5',
              borderBottom: '1px solid #e0e0e0'
            },
            '& .MuiDataGrid-cell': {
              borderBottom: '1px solid #f0f0f0',
              textAlign: 'right'
            },
            '& .MuiDataGrid-row:hover': {
              backgroundColor: '#f9f9f9'
            }
          }}
          localeText={{
            noRowsLabel: 'لا توجد مستخدمين',
            footerRowSelected: (count) => `${count} صف محدد`,
            footerTotalRows: 'إجمالي الصفوف:',
            footerPaginationRowsPerPage: 'صفوف لكل صفحة:',
          }}
        />
      </Box>

      {/* Dialog للإضافة/التعديل */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {selectedUser
              ? (viewMode ? 'عرض تفاصيل المستخدم' : 'تعديل المستخدم')
              : 'إضافة مستخدم جديد'
            }
          </Typography>
          <IconButton onClick={handleCloseDialog} size="small">
            <span style={{ fontSize: '20px' }}>✕</span>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <UserForm
            user={selectedUser}
            onSuccess={() => {
              queryClient.invalidateQueries('users')
              handleCloseDialog()
            }}
            readOnly={viewMode || !hasPermission('users', selectedUser ? 'update' : 'create')}
            viewMode={viewMode}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog تأكيد الحذف */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">تأكيد الحذف</Typography>
          <IconButton onClick={() => setDeleteDialogOpen(false)} size="small">
            <span style={{ fontSize: '20px' }}>✕</span>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف المستخدم "{userToDelete?.username}"؟
          </Typography>
          {userToDelete?._count?.clients > 0 && (
            <Typography color="warning.main" sx={{ mt: 1 }}>
              تحذير: هذا المستخدم لديه {userToDelete._count.clients} عميل مرتبط به.
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>إلغاء</Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            disabled={deleteMutation.isLoading}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default UsersPage
