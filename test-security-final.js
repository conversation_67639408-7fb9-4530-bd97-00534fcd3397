const http = require('http');

console.log('🔐 اختبار الحماية النهائي');
console.log('====================');

function testAPI(path) {
  return new Promise((resolve) => {
    const options = {
      hostname: '***********',
      port: 8080,
      path: path,
      method: 'GET',
      timeout: 10000
    };

    console.log(`\n🧪 اختبار: ${path}`);
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`📊 Status: ${res.statusCode}`);
        
        if (res.statusCode === 401) {
          console.log('✅ محمي بشكل صحيح - 401 Unauthorized');
          try {
            const jsonData = JSON.parse(data);
            if (jsonData.message && jsonData.message.includes('Token مطلوب')) {
              console.log('✅ رسالة الحماية صحيحة');
            }
          } catch (e) {
            console.log('⚠️ استجابة غير JSON');
          }
        } else if (res.statusCode === 200) {
          console.log('❌ غير محمي - يعرض البيانات!');
          try {
            const jsonData = JSON.parse(data);
            if (jsonData.data && jsonData.data.length > 0) {
              console.log(`📋 عدد السجلات المعروضة: ${jsonData.data.length}`);
            }
          } catch (e) {
            console.log('📄 يعرض محتوى HTML أو نص');
          }
        } else {
          console.log(`⚠️ Status غير متوقع: ${res.statusCode}`);
        }
        
        resolve({ path, status: res.statusCode, protected: res.statusCode === 401 });
      });
    });

    req.on('error', (err) => {
      console.log(`❌ خطأ في الاتصال: ${err.message}`);
      resolve({ path, status: 'ERROR', error: err.message });
    });

    req.on('timeout', () => {
      console.log('⏰ انتهت مهلة الاتصال');
      req.destroy();
      resolve({ path, status: 'TIMEOUT' });
    });

    req.end();
  });
}

async function runTest() {
  const protectedAPIs = [
    '/api/users?page=1&limit=10',
    '/api/clients?page=1&limit=10',
    '/api/agents?page=1&limit=10',
    '/api/data-records?page=1&limit=10',
    '/api/dashboard/stats',
    '/api/security/stats'
  ];

  const publicAPIs = [
    '/api/external/health',
    '/api/external/test-db'
  ];

  console.log('\n🚫 اختبار APIs المحمية (يجب أن ترجع 401):');
  console.log('===========================================');
  
  let protectedCount = 0;
  let totalProtected = protectedAPIs.length;
  
  for (const api of protectedAPIs) {
    const result = await testAPI(api);
    if (result.protected) protectedCount++;
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n✅ اختبار APIs العامة (يجب أن ترجع 200):');
  console.log('=========================================');
  
  let publicCount = 0;
  let totalPublic = publicAPIs.length;
  
  for (const api of publicAPIs) {
    const result = await testAPI(api);
    if (result.status === 200) publicCount++;
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n📋 ملخص النتائج:');
  console.log('================');
  console.log(`🔒 APIs محمية: ${protectedCount}/${totalProtected}`);
  console.log(`🌐 APIs عامة: ${publicCount}/${totalPublic}`);
  
  if (protectedCount === totalProtected && publicCount === totalPublic) {
    console.log('🎉 الحماية تعمل بشكل مثالي!');
  } else {
    console.log('⚠️ يوجد مشاكل في الحماية');
  }
}

runTest().catch(console.error);
