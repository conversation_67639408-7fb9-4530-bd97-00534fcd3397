// تشغيل الخادم مباشرة
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting secure server directly...');

const serverPath = path.join(__dirname, 'server', 'secure-server.js');
console.log('Server path:', serverPath);

const server = spawn('node', [serverPath], {
  stdio: 'inherit',
  cwd: __dirname
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

server.on('close', (code) => {
  console.log(`🛑 Server exited with code: ${code}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  server.kill();
  process.exit(0);
});
