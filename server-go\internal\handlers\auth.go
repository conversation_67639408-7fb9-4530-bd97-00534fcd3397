package handlers

import (
	"encoding/json"
	"net/http"
	"os"
	
	"golang.org/x/crypto/bcrypt"
	"yemclient-server/internal/database"
	"yemclient-server/internal/models"
)

func LoginHandler(w http.ResponseWriter, r *http.Request) {
	var loginReq struct {
		LoginName string `json:"loginName"`
		Password  string `json:"password"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&loginReq); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	
	// Check if user exists
	var user models.User
	if err := database.DB.Where("login_name = ?", loginReq.LoginName).First(&user).Error; err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}
	
	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginReq.Password)); err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}
	
	// Check if account is active
	if !user.IsActive {
		http.Error(w, "Account is inactive", http.StatusForbidden)
		return
	}
	
	// Update JWT token generation
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		secret = "default-secret"
	}
	
	// Use secret for token generation
	token := "generated-jwt-token" // TODO: implement proper JWT token generation using the secret
	
	// Return success response
	json.NewEncoder(w).Encode(map[string]interface{}{
		"token": token,
		"user":  user,
	})
}

func LogoutHandler(w http.ResponseWriter, r *http.Request) {
	// In a real implementation, you would invalidate the token
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "Logged out successfully"})
}

func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Simplified auth check - in real app validate JWT
		token := r.Header.Get("Authorization")
		if token == "" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		
		next.ServeHTTP(w, r)
	})
}
