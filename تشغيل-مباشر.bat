@echo off
chcp 65001 >nul
title تشغيل خادم YemClient
color 0B

echo.
echo ========================================
echo        تشغيل خادم إدارة العملاء
echo ========================================
echo.

echo 🛑 إيقاف أي خوادم موجودة...
powershell -Command "Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }" 2>nul

echo.
echo 📂 الانتقال إلى مجلد الخادم...
cd /d "%~dp0server"

echo 🔍 التحقق من وجود الملف...
if not exist "working-server.js" (
    echo ❌ ملف working-server.js غير موجود!
    echo 📍 المجلد الحالي: %cd%
    echo.
    echo اضغط أي مفتاح للإغلاق...
    pause >nul
    exit /b 1
)

echo ✅ الملف موجود
echo.
echo 🚀 تشغيل الخادم...
echo ========================================
echo.

node working-server.js

echo.
echo ========================================
echo 🛑 تم إيقاف الخادم
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
